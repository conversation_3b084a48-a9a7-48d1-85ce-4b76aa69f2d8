/**
 * Web Worker for parameter assignment operations
 * Handles heavy computations off the main thread for better UI responsiveness
 */

import * as turf from '@turf/turf';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import type { ShorelineSegment, Parameter } from '../types';
import { <PERSON><PERSON><PERSON>Buffer, SegmentMetadataBuffer, MemoryMonitor } from '../utils/memoryPool';
import { compressData, decompressData } from '../utils/compression';

// Types for worker communication
interface ParameterAssignmentTask {
  id: string;
  type: 'POLYGON_INTERSECTION' | 'BATCH_VALUE_ASSIGNMENT' | 'CVI_CALCULATION';
  payload: any;
}

interface ProgressUpdate {
  type: 'PROGRESS';
  payload: {
    taskId: string;
    completed: number;
    total: number;
    percentage: number;
    memoryUsage?: any;
  };
}

interface TaskResult {
  type: 'RESULT';
  payload: {
    taskId: string;
    result: any;
    processingTime: number;
    memoryStats?: any;
  };
}

interface ErrorMessage {
  type: 'ERROR';
  payload: {
    taskId: string;
    error: string;
  };
}

// Spatial indexing for fast intersection detection
interface SpatialIndex {
  segments: Map<string, { bounds: [number, number, number, number]; segment: ShorelineSegment }>;
  grid: Map<string, string[]>;
  cellSize: number;
}

let currentTask: string | null = null;
let isProcessing = false;
let memoryMonitor: MemoryMonitor;

// Initialize memory monitor
try {
  memoryMonitor = MemoryMonitor.getInstance();
} catch (error) {
  console.warn('Memory monitoring not available in worker:', error);
}

/**
 * Build optimized spatial index for segments
 */
function buildSpatialIndex(segments: ShorelineSegment[]): SpatialIndex {
  console.log(`Building spatial index for ${segments.length} segments...`);

  // Calculate bounds for all segments
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

  const segmentBounds = new Map<string, [number, number, number, number]>();

  segments.forEach(segment => {
    const coords = segment.geometry.coordinates;
    let segMinX = Infinity, segMinY = Infinity, segMaxX = -Infinity, segMaxY = -Infinity;

    coords.forEach(coord => {
      const [x, y] = coord;
      segMinX = Math.min(segMinX, x);
      segMinY = Math.min(segMinY, y);
      segMaxX = Math.max(segMaxX, x);
      segMaxY = Math.max(segMaxY, y);

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    });

    segmentBounds.set(segment.id, [segMinX, segMinY, segMaxX, segMaxY]);
  });

  // Calculate optimal cell size
  const width = maxX - minX;
  const height = maxY - minY;
  const area = width * height;
  const avgSegmentArea = area / segments.length;
  const cellSize = Math.sqrt(avgSegmentArea) * 2; // Heuristic for good performance

  const spatialIndex: SpatialIndex = {
    segments: new Map(),
    grid: new Map(),
    cellSize
  };

  // Populate spatial index
  segments.forEach(segment => {
    const bounds = segmentBounds.get(segment.id)!;
    spatialIndex.segments.set(segment.id, { bounds, segment });

    // Add to grid cells
    const startCellX = Math.floor((bounds[0] - minX) / cellSize);
    const startCellY = Math.floor((bounds[1] - minY) / cellSize);
    const endCellX = Math.floor((bounds[2] - minX) / cellSize);
    const endCellY = Math.floor((bounds[3] - minY) / cellSize);

    for (let x = startCellX; x <= endCellX; x++) {
      for (let y = startCellY; y <= endCellY; y++) {
        const cellKey = `${x},${y}`;
        if (!spatialIndex.grid.has(cellKey)) {
          spatialIndex.grid.set(cellKey, []);
        }
        spatialIndex.grid.get(cellKey)!.push(segment.id);
      }
    }
  });

  console.log(`Spatial index built: ${spatialIndex.grid.size} cells, ${spatialIndex.segments.size} segments`);
  return spatialIndex;
}

/**
 * Find segments intersecting with polygon using spatial indexing
 */
async function findIntersectingSegments(
  polygon: GeoJSONPolygon,
  segments: ShorelineSegment[],
  onProgress?: (completed: number, total: number) => void
): Promise<string[]> {
  console.log(`Finding intersecting segments for polygon with ${segments.length} total segments...`);

  const startTime = performance.now();
  const spatialIndex = buildSpatialIndex(segments);
  const polygonTurf = turf.polygon(polygon.coordinates);
  const polygonBounds = turf.bbox(polygonTurf);

  // Get candidate segments from spatial index
  const candidateIds = getCandidateSegments(polygonBounds, spatialIndex);
  console.log(`Spatial index reduced candidates from ${segments.length} to ${candidateIds.length}`);

  const intersectingIds: string[] = [];
  const batchSize = 100;
  let processed = 0;

  // Process candidates in batches
  for (let i = 0; i < candidateIds.length; i += batchSize) {
    const batch = candidateIds.slice(i, i + batchSize);

    for (const segmentId of batch) {
      const segmentData = spatialIndex.segments.get(segmentId);
      if (segmentData) {
        try {
          if (turf.booleanIntersects(segmentData.segment.geometry, polygonTurf)) {
            intersectingIds.push(segmentId);
          }
        } catch (error) {
          console.warn(`Error checking intersection for segment ${segmentId}:`, error);
        }
      }
      processed++;
    }

    // Report progress and yield control
    if (onProgress) {
      onProgress(processed, candidateIds.length);
    }

    // Yield control every batch
    await new Promise(resolve => setTimeout(resolve, 1));
  }

  const endTime = performance.now();
  console.log(`Found ${intersectingIds.length} intersecting segments in ${(endTime - startTime).toFixed(2)}ms`);

  return intersectingIds;
}

/**
 * Get candidate segments from spatial index based on bounding box
 */
function getCandidateSegments(bounds: [number, number, number, number], spatialIndex: SpatialIndex): string[] {
  const [minX, minY, maxX, maxY] = bounds;
  const candidateIds = new Set<string>();

  const startCellX = Math.floor(minX / spatialIndex.cellSize);
  const startCellY = Math.floor(minY / spatialIndex.cellSize);
  const endCellX = Math.floor(maxX / spatialIndex.cellSize);
  const endCellY = Math.floor(maxY / spatialIndex.cellSize);

  for (let x = startCellX; x <= endCellX; x++) {
    for (let y = startCellY; y <= endCellY; y++) {
      const cellKey = `${x},${y}`;
      const segmentIds = spatialIndex.grid.get(cellKey);
      if (segmentIds) {
        segmentIds.forEach(id => candidateIds.add(id));
      }
    }
  }

  return Array.from(candidateIds);
}

/**
 * Optimized batch value assignment with memory management
 */
async function batchValueAssignment(
  segments: ShorelineSegment[],
  selectedSegmentIds: string[],
  parameter: Parameter,
  value: string,
  vulnerability: number,
  onProgress?: (completed: number, total: number) => void
): Promise<ShorelineSegment[]> {
  console.log(`Batch value assignment: ${selectedSegmentIds.length} segments for parameter "${parameter.name}"`);

  const startTime = performance.now();
  const selectedSet = new Set(selectedSegmentIds);
  const batchSize = 500;
  let processed = 0;

  // Create a copy of segments array for modification
  const updatedSegments = [...segments];

  // Process segments in batches
  for (let i = 0; i < updatedSegments.length; i += batchSize) {
    const batch = updatedSegments.slice(i, i + batchSize);

    batch.forEach((segment, batchIndex) => {
      const globalIndex = i + batchIndex;
      if (selectedSet.has(segment.id)) {
        // Apply parameter value
        if (!updatedSegments[globalIndex].parameters) {
          updatedSegments[globalIndex].parameters = {};
        }

        updatedSegments[globalIndex].parameters[parameter.id] = {
          value,
          vulnerability
        };

        // Update properties for compatibility
        if (!updatedSegments[globalIndex].properties.values) {
          updatedSegments[globalIndex].properties.values = {};
        }
        updatedSegments[globalIndex].properties.values[parameter.id] = value;
      }
      processed++;
    });

    // Report progress and yield control
    if (onProgress) {
      onProgress(processed, updatedSegments.length);
    }

    // Yield control every batch
    await new Promise(resolve => setTimeout(resolve, 1));
  }

  const endTime = performance.now();
  console.log(`Batch value assignment completed in ${(endTime - startTime).toFixed(2)}ms`);

  return updatedSegments;
}

/**
 * Optimized CVI calculation with parallel processing
 */
async function calculateCVI(
  segments: ShorelineSegment[],
  parameters: Parameter[],
  formula: any,
  onProgress?: (completed: number, total: number) => void
): Promise<{ [segmentId: string]: number }> {
  console.log(`CVI calculation for ${segments.length} segments using ${formula.name}`);

  const startTime = performance.now();
  const cviScores: { [segmentId: string]: number } = {};
  const batchSize = 200;
  let processed = 0;

  // Pre-validate segments and parameters
  const validSegments = segments.filter(segment => {
    return parameters.every(param =>
      segment.parameters?.[param.id] !== undefined
    );
  });

  console.log(`Processing ${validSegments.length} valid segments out of ${segments.length} total`);

  // Process segments in batches
  for (let i = 0; i < validSegments.length; i += batchSize) {
    const batch = validSegments.slice(i, i + batchSize);

    batch.forEach(segment => {
      try {
        const values: number[] = [];
        const weights: number[] = [];

        parameters.forEach(param => {
          const paramValue = segment.parameters?.[param.id];
          if (paramValue) {
            values.push(paramValue.vulnerability);
            weights.push(param.weight || 1);
          }
        });

        if (values.length === 0) {
          cviScores[segment.id] = 0;
          return;
        }

        // Apply formula (simplified - extend based on actual formula types)
        let score: number;
        switch (formula.type) {
          case 'geometric-mean':
            score = Math.pow(values.reduce((a, b) => a * b, 1), 1 / values.length);
            break;
          case 'arithmetic-mean':
            score = values.reduce((a, b) => a + b, 0) / values.length;
            break;
          case 'traditional':
            // Weighted average
            const weightedSum = values.reduce((sum, val, idx) => sum + val * weights[idx], 0);
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
            score = weightedSum / totalWeight;
            break;
          default:
            score = values.reduce((a, b) => a + b, 0) / values.length;
        }

        cviScores[segment.id] = score;
      } catch (error) {
        console.warn(`Error calculating CVI for segment ${segment.id}:`, error);
        cviScores[segment.id] = 0;
      }
      processed++;
    });

    // Report progress and yield control
    if (onProgress) {
      onProgress(processed, validSegments.length);
    }

    // Yield control every batch
    await new Promise(resolve => setTimeout(resolve, 1));
  }

  const endTime = performance.now();
  console.log(`CVI calculation completed in ${(endTime - startTime).toFixed(2)}ms`);

  return cviScores;
}

// Worker message handler
self.onmessage = async (event: MessageEvent<ParameterAssignmentTask>) => {
  const { id, type, payload } = event.data;

  if (isProcessing && currentTask !== id) {
    const errorMessage: ErrorMessage = {
      type: 'ERROR',
      payload: {
        taskId: id,
        error: 'Worker is busy with another task'
      }
    };
    self.postMessage(errorMessage);
    return;
  }

  if (type === 'POLYGON_INTERSECTION') {
    currentTask = id;
    isProcessing = true;

    try {
      const startTime = performance.now();

      const progressCallback = (completed: number, total: number) => {
        const progressUpdate: ProgressUpdate = {
          type: 'PROGRESS',
          payload: {
            taskId: id,
            completed,
            total,
            percentage: (completed / total) * 100,
            memoryUsage: memoryMonitor?.getCurrentMemoryUsage()
          }
        };
        self.postMessage(progressUpdate);
      };

      const intersectingIds = await findIntersectingSegments(
        payload.polygon,
        payload.segments,
        progressCallback
      );

      const endTime = performance.now();
      const processingTime = (endTime - startTime) / 1000;

      const result: TaskResult = {
        type: 'RESULT',
        payload: {
          taskId: id,
          result: intersectingIds,
          processingTime,
          memoryStats: memoryMonitor?.getMemoryUsage()
        }
      };

      self.postMessage(result);
    } catch (error) {
      const errorMessage: ErrorMessage = {
        type: 'ERROR',
        payload: {
          taskId: id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      self.postMessage(errorMessage);
    } finally {
      currentTask = null;
      isProcessing = false;
    }
  }

  else if (type === 'BATCH_VALUE_ASSIGNMENT') {
    currentTask = id;
    isProcessing = true;

    try {
      const startTime = performance.now();

      const progressCallback = (completed: number, total: number) => {
        const progressUpdate: ProgressUpdate = {
          type: 'PROGRESS',
          payload: {
            taskId: id,
            completed,
            total,
            percentage: (completed / total) * 100,
            memoryUsage: memoryMonitor?.getCurrentMemoryUsage()
          }
        };
        self.postMessage(progressUpdate);
      };

      const updatedSegments = await batchValueAssignment(
        payload.segments,
        payload.selectedSegmentIds,
        payload.parameter,
        payload.value,
        payload.vulnerability,
        progressCallback
      );

      const endTime = performance.now();
      const processingTime = (endTime - startTime) / 1000;

      const result: TaskResult = {
        type: 'RESULT',
        payload: {
          taskId: id,
          result: updatedSegments,
          processingTime,
          memoryStats: memoryMonitor?.getMemoryUsage()
        }
      };

      self.postMessage(result);
    } catch (error) {
      const errorMessage: ErrorMessage = {
        type: 'ERROR',
        payload: {
          taskId: id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      self.postMessage(errorMessage);
    } finally {
      currentTask = null;
      isProcessing = false;
    }
  }

  else if (type === 'CVI_CALCULATION') {
    currentTask = id;
    isProcessing = true;

    try {
      const startTime = performance.now();

      const progressCallback = (completed: number, total: number) => {
        const progressUpdate: ProgressUpdate = {
          type: 'PROGRESS',
          payload: {
            taskId: id,
            completed,
            total,
            percentage: (completed / total) * 100,
            memoryUsage: memoryMonitor?.getCurrentMemoryUsage()
          }
        };
        self.postMessage(progressUpdate);
      };

      const cviScores = await calculateCVI(
        payload.segments,
        payload.parameters,
        payload.formula,
        progressCallback
      );

      const endTime = performance.now();
      const processingTime = (endTime - startTime) / 1000;

      const result: TaskResult = {
        type: 'RESULT',
        payload: {
          taskId: id,
          result: cviScores,
          processingTime,
          memoryStats: memoryMonitor?.getMemoryUsage()
        }
      };

      self.postMessage(result);
    } catch (error) {
      const errorMessage: ErrorMessage = {
        type: 'ERROR',
        payload: {
          taskId: id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      self.postMessage(errorMessage);
    } finally {
      currentTask = null;
      isProcessing = false;
    }
  }
};
