/**
 * Web Worker for segmentation processing
 * Handles all heavy computation off the main thread
 */

import { optimizedGeodesicDistance, batchGeodesicDistances } from '../utils/geodesic';
import { CoordinateBuffer, SegmentMetadataBuffer, MemoryMonitor } from '../utils/memoryPool';
import { compressSegments } from '../utils/compression';

// Types for worker communication
interface SegmentationTask {
  id: string;
  type: 'SEGMENT_SHORELINE';
  payload: {
    geoJSON: any;
    resolution: number;
    options: {
      batchSize?: number;
      useCompression?: boolean;
    };
  };
}

interface ProgressUpdate {
  type: 'PROGRESS';
  payload: {
    taskId: string;
    completed: number;
    total: number;
    percentage: number;
    currentFeature: number;
    totalFeatures: number;
    memoryUsage?: any;
  };
}

interface SegmentationResult {
  type: 'RESULT';
  payload: {
    taskId: string;
    segments: any[];
    processingTime: number;
    memoryStats: any;
    compressionStats?: any;
  };
}

interface ErrorMessage {
  type: 'ERROR';
  payload: {
    taskId: string;
    error: string;
  };
}

type WorkerMessage = ProgressUpdate | SegmentationResult | ErrorMessage;

// Worker state
let currentTask: string | null = null;
let isProcessing = false;
const memoryMonitor = MemoryMonitor.getInstance();

/**
 * Optimized interpolation function
 */
function interpolateCoordinate(
  coordinates: Float32Array,
  distances: Float32Array,
  targetDistance: number,
  coordinateCount: number
): [number, number] {
  if (targetDistance <= 0) {
    return [coordinates[0], coordinates[1]];
  }

  const lastIndex = coordinateCount - 1;
  if (targetDistance >= distances[lastIndex]) {
    return [coordinates[lastIndex * 2], coordinates[lastIndex * 2 + 1]];
  }

  // Binary search for efficiency
  let left = 0;
  let right = lastIndex;

  while (left < right) {
    const mid = Math.floor((left + right) / 2);
    if (distances[mid] < targetDistance) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }

  const i = left;
  if (i === 0) {
    return [coordinates[0], coordinates[1]];
  }

  const segmentStart = distances[i - 1];
  const segmentEnd = distances[i];
  const segmentLength = segmentEnd - segmentStart;

  if (segmentLength === 0) {
    return [coordinates[(i - 1) * 2], coordinates[(i - 1) * 2 + 1]];
  }

  const ratio = (targetDistance - segmentStart) / segmentLength;
  const startLon = coordinates[(i - 1) * 2];
  const startLat = coordinates[(i - 1) * 2 + 1];
  const endLon = coordinates[i * 2];
  const endLat = coordinates[i * 2 + 1];

  return [
    startLon + (endLon - startLon) * ratio,
    startLat + (endLat - startLat) * ratio
  ];
}

/**
 * Extract original path coordinates for a segment to preserve shoreline accuracy
 * This ensures segments follow the exact original shoreline path
 */
function extractOriginalPathCoordinates(
  coordinates: Float32Array,
  distances: Float32Array,
  startDistance: number,
  endDistance: number,
  coordinateCount: number
): [number, number][] {
  const result: [number, number][] = [];

  // Find the indices that bracket our segment distances
  let startIndex = -1;
  let endIndex = -1;

  // Find start index - the last coordinate before or at startDistance
  for (let i = 0; i < coordinateCount; i++) {
    if (distances[i] <= startDistance) {
      startIndex = i;
    } else {
      break;
    }
  }

  // Find end index - the first coordinate at or after endDistance
  for (let i = startIndex + 1; i < coordinateCount; i++) {
    if (distances[i] >= endDistance) {
      endIndex = i;
      break;
    }
  }

  // If we couldn't find proper indices, fall back to interpolated points
  if (startIndex === -1 || endIndex === -1) {
    const startCoord = interpolateCoordinate(coordinates, distances, startDistance, coordinateCount);
    const endCoord = interpolateCoordinate(coordinates, distances, endDistance, coordinateCount);
    return [startCoord, endCoord];
  }

  // Add interpolated start point if it's not exactly at a coordinate
  if (distances[startIndex] < startDistance) {
    const startCoord = interpolateCoordinate(coordinates, distances, startDistance, coordinateCount);
    result.push(startCoord);
  } else {
    result.push([coordinates[startIndex * 2], coordinates[startIndex * 2 + 1]]);
  }

  // Add all intermediate coordinates
  for (let i = startIndex + 1; i < endIndex; i++) {
    result.push([coordinates[i * 2], coordinates[i * 2 + 1]]);
  }

  // Add interpolated end point if it's not exactly at a coordinate
  if (distances[endIndex] > endDistance) {
    const endCoord = interpolateCoordinate(coordinates, distances, endDistance, coordinateCount);
    result.push(endCoord);
  } else {
    result.push([coordinates[endIndex * 2], coordinates[endIndex * 2 + 1]]);
  }

  // Ensure we have at least 2 points
  if (result.length < 2) {
    const startCoord = interpolateCoordinate(coordinates, distances, startDistance, coordinateCount);
    const endCoord = interpolateCoordinate(coordinates, distances, endDistance, coordinateCount);
    return [startCoord, endCoord];
  }

  return result;
}

/**
 * Process a single line with optimized streaming
 */
async function processLineOptimized(
  coordinates: [number, number][],
  resolution: number,
  baseSegmentId: number,
  lineIndex: number,
  onProgress?: (processed: number) => void
): Promise<any[]> {
  const coordinateCount = coordinates.length;

  // Convert to Float32Array for better performance
  const coordBuffer = new Float32Array(coordinateCount * 2);
  for (let i = 0; i < coordinateCount; i++) {
    coordBuffer[i * 2] = coordinates[i][0];     // longitude
    coordBuffer[i * 2 + 1] = coordinates[i][1]; // latitude
  }

  // Calculate distances using optimized batch function
  const distances = new Float32Array(coordinateCount);
  distances[0] = 0;

  for (let i = 1; i < coordinateCount; i++) {
    const lat1 = coordBuffer[(i - 1) * 2 + 1];
    const lon1 = coordBuffer[(i - 1) * 2];
    const lat2 = coordBuffer[i * 2 + 1];
    const lon2 = coordBuffer[i * 2];

    const segmentDistance = optimizedGeodesicDistance(lat1, lon1, lat2, lon2);
    distances[i] = distances[i - 1] + segmentDistance;
  }

  const totalLength = distances[coordinateCount - 1];
  const numSegments = Math.ceil(totalLength / resolution);
  const actualResolution = totalLength / numSegments;

  const segments = [];
  const batchSize = 1000; // Process in larger batches

  for (let i = 0; i < numSegments; i++) {
    // Check for memory pressure
    if (i % 100 === 0 && memoryMonitor.isMemoryPressureHigh()) {
      memoryMonitor.forceGarbageCollection();
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    const startDist = i * actualResolution;
    const endDist = Math.min((i + 1) * actualResolution, totalLength);

    const startCoord = interpolateCoordinate(coordBuffer, distances, startDist, coordinateCount);
    const endCoord = interpolateCoordinate(coordBuffer, distances, endDist, coordinateCount);

    // Calculate segment length
    const segmentLength = optimizedGeodesicDistance(
      startCoord[1], startCoord[0], endCoord[1], endCoord[0]
    );

    // Extract original path coordinates to preserve shoreline accuracy
    const segmentCoordinates = extractOriginalPathCoordinates(
      coordBuffer, distances, startDist, endDist, coordinateCount
    );

    const segment = {
      type: 'Feature',
      id: `segment-${baseSegmentId + i}`,
      geometry: {
        type: 'LineString',
        coordinates: segmentCoordinates
      },
      properties: {
        id: `segment-${baseSegmentId + i}`,
        index: i,
        lineIndex,
        length: segmentLength,
        values: {}
      },
      parameters: {}
    };

    segments.push(segment);

    // Report progress periodically
    if (onProgress && i % 100 === 0) {
      onProgress(i);
    }

    // Yield control periodically
    if (i % batchSize === 0) {
      await new Promise(resolve => setTimeout(resolve, 1));
    }
  }

  return segments;
}

/**
 * Main segmentation function
 */
async function segmentShoreline(
  geoJSON: any,
  resolution: number,
  options: any = {}
): Promise<any[]> {
  const startTime = performance.now();
  let totalProcessed = 0;
  let allSegments: any[] = [];

  // Calculate total features for progress tracking
  const totalFeatures = geoJSON.features.reduce((sum: number, f: any) =>
    sum + (f.geometry.type === 'MultiLineString' ? f.geometry.coordinates.length : 1), 0);

  let currentFeatureIndex = 0;
  let segmentId = 1;

  for (let featureIndex = 0; featureIndex < geoJSON.features.length; featureIndex++) {
    const feature = geoJSON.features[featureIndex];
    const coordinateArrays = feature.geometry.type === 'MultiLineString'
      ? feature.geometry.coordinates
      : [feature.geometry.coordinates];

    for (let lineIndex = 0; lineIndex < coordinateArrays.length; lineIndex++) {
      const coordinates = coordinateArrays[lineIndex];

      const lineSegments = await processLineOptimized(
        coordinates,
        resolution,
        segmentId,
        lineIndex,
        (processed) => {
          // Send progress update
          const message: ProgressUpdate = {
            type: 'PROGRESS',
            payload: {
              taskId: currentTask!,
              completed: totalProcessed + processed,
              total: totalProcessed + Math.ceil(coordinates.length * resolution / 100), // Rough estimate
              percentage: ((totalProcessed + processed) / (totalProcessed + coordinates.length)) * 100,
              currentFeature: currentFeatureIndex + 1,
              totalFeatures,
              memoryUsage: memoryMonitor.getMemoryUsage()
            }
          };
          self.postMessage(message);
        }
      );

      allSegments = allSegments.concat(lineSegments);
      totalProcessed += lineSegments.length;
      segmentId += lineSegments.length;
      currentFeatureIndex++;

      // Send progress update after each line
      const message: ProgressUpdate = {
        type: 'PROGRESS',
        payload: {
          taskId: currentTask!,
          completed: totalProcessed,
          total: totalProcessed + 1000, // Will be updated as we process
          percentage: (currentFeatureIndex / totalFeatures) * 100,
          currentFeature: currentFeatureIndex,
          totalFeatures,
          memoryUsage: memoryMonitor.getMemoryUsage()
        }
      };
      self.postMessage(message);
    }
  }

  const endTime = performance.now();
  const processingTime = (endTime - startTime) / 1000;

  return allSegments;
}

/**
 * Handle incoming messages
 */
self.onmessage = async (event: MessageEvent<SegmentationTask>) => {
  const { id, type, payload } = event.data;

  if (type === 'SEGMENT_SHORELINE') {
    if (isProcessing) {
      const errorMessage: ErrorMessage = {
        type: 'ERROR',
        payload: {
          taskId: id,
          error: 'Worker is already processing a task'
        }
      };
      self.postMessage(errorMessage);
      return;
    }

    currentTask = id;
    isProcessing = true;

    try {
      const startTime = performance.now();

      const segments = await segmentShoreline(
        payload.geoJSON,
        payload.resolution,
        payload.options
      );

      const endTime = performance.now();
      const processingTime = (endTime - startTime) / 1000;

      const result: SegmentationResult = {
        type: 'RESULT',
        payload: {
          taskId: id,
          segments,
          processingTime,
          memoryStats: memoryMonitor.getMemoryUsage()
        }
      };

      self.postMessage(result);
    } catch (error) {
      const errorMessage: ErrorMessage = {
        type: 'ERROR',
        payload: {
          taskId: id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      self.postMessage(errorMessage);
    } finally {
      currentTask = null;
      isProcessing = false;
    }
  }
};
