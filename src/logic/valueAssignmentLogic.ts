import { indexedDBService } from '../services/indexedDBService';
import type { Parameter, ShorelineSegment, ParameterValue, ShorelineSegmentProperties } from '../types';
import * as turf from '@turf/turf';

export const applyParameterValueToSegments = async (
    segments: ShorelineSegment[],
    selectedSegmentIds: string[],
    activeParameter: Parameter,
    valueToApply: string,
    vulnerabilityToApply: number
): Promise<ShorelineSegment[]> => {
    console.log(`Applying value "${valueToApply}" (vuln: ${vulnerabilityToApply}) for "${activeParameter.name}" to ${selectedSegmentIds.length} segments`);

    // Create a Set for O(1) lookup performance
    const selectedIdsSet = new Set(selectedSegmentIds);

    // Prepare parameter value
    let paramValue: ParameterValue;
    if (activeParameter.type === 'numerical') {
        const numValue = parseFloat(valueToApply);
        if (isNaN(numValue)) {
            throw new Error(`Invalid numerical value: ${valueToApply}`);
        }
        paramValue = { type: 'numerical', value: numValue, vulnerability: vulnerabilityToApply };
    } else {
        paramValue = { type: 'categorical', value: valueToApply, vulnerability: vulnerabilityToApply };
    }

    // Optimized update: only process segments that need updating
    let updateOccurred = false;
    const updatedSegments = segments.map(segment => {
        if (!selectedIdsSet.has(segment.id)) {
            return segment; // No change needed
        }

        const existingValue = segment.parameters?.[activeParameter.id];
        const needsUpdate = !existingValue ||
                           existingValue.value !== paramValue.value ||
                           existingValue.vulnerability !== paramValue.vulnerability;

        if (!needsUpdate) {
            return segment; // No change needed
        }

        updateOccurred = true;

        // Update both direct parameters and properties.parameters
        const updatedDirectParameters = { ...segment.parameters, [activeParameter.id]: paramValue };
        const currentProperties = segment.properties || { id: segment.id } as ShorelineSegmentProperties;
        const currentPropertiesParams = currentProperties.parameters && typeof currentProperties.parameters === 'object'
                                       ? currentProperties.parameters
                                       : {};
        const updatedProperties: ShorelineSegmentProperties = {
            ...currentProperties,
            parameters: { ...currentPropertiesParams, [activeParameter.id]: paramValue }
        };

        return { ...segment, parameters: updatedDirectParameters, properties: updatedProperties };
    });

    // Always return a new array reference to ensure React detects changes
    const finalSegments = updateOccurred ? updatedSegments : [...segments];

    // Optimized storage: defer database updates for better performance
    if (updateOccurred) {
        try {
            console.log(`Updated ${selectedSegmentIds.length} segments in memory`);

            // For large datasets, defer the database write to avoid blocking the UI
            if (segments.length > 10000) {
                // Use setTimeout to defer the database write and keep UI responsive
                setTimeout(async () => {
                    try {
                        await storeSegmentsOptimized(finalSegments);
                        console.log("Background database update completed");
                    } catch (error) {
                        console.error('Background database update failed:', error);
                    }
                }, 100);
            } else {
                // For smaller datasets, update immediately
                await storeSegmentsOptimized(finalSegments);
                console.log("Database updated successfully");
            }
        } catch (error) {
            console.error('Failed to update segments:', error);
            throw new Error(`Failed to save changes: ${error instanceof Error ? error.message : String(error)}`);
        }
    } else {
        console.log("No segment values changed, skipping update.");
    }

    return finalSegments;
};

// Optimized storage function that minimizes memory allocation
export async function storeSegmentsOptimized(segments: ShorelineSegment[], processingTime?: number): Promise<void> {
    const startTime = performance.now();

    // Convert segments to features efficiently
    const features = new Array(segments.length);
    let totalLength = 0;
    for (let i = 0; i < segments.length; i++) {
        features[i] = {
            type: 'Feature' as const,
            geometry: segments[i].geometry,
            properties: segments[i].properties,
        };
        totalLength += segments[i].properties?.length || 0;
    }

    // Calculate bounds for the segments
    const bounds = calculateSegmentBounds(segments);

    // Store the segment data
    await indexedDBService.storeShorelineData('current-segments', {
        type: 'FeatureCollection',
        features
    }, bounds);

    // Store metadata for fast access
    const metadata = {
        id: 'current-segments',
        totalSegments: segments.length,
        bounds: bounds || [[-90, -180], [90, 180]] as [[number, number], [number, number]],
        timestamp: Date.now(),
        isMemoryOptimized: segments.length > 10000,
        averageSegmentLength: segments.length > 0 ? totalLength / segments.length : 0,
        processingTime: processingTime || 0
    };

    await indexedDBService.storeSegmentMetadata(metadata);

    const endTime = performance.now();
    console.log(`Database storage completed in ${(endTime - startTime).toFixed(2)}ms`);
}

/**
 * Streaming parameter value assignment that works directly with IndexedDB
 * without loading all segments into memory
 */
export async function applyParameterValueToSegmentsStreaming(
    selectedSegmentIds: string[],
    activeParameter: Parameter,
    valueToApply: string,
    vulnerabilityToApply: number
): Promise<void> {
    console.log(`Streaming value assignment: "${valueToApply}" (vuln: ${vulnerabilityToApply}) for "${activeParameter.name}" to ${selectedSegmentIds.length} segments`);

    // Prepare parameter value
    let paramValue: ParameterValue;
    if (activeParameter.type === 'numerical') {
        const numValue = parseFloat(valueToApply);
        if (isNaN(numValue)) {
            throw new Error(`Invalid numerical value: ${valueToApply}`);
        }
        paramValue = { type: 'numerical', value: numValue, vulnerability: vulnerabilityToApply };
    } else {
        paramValue = { type: 'categorical', value: valueToApply, vulnerability: vulnerabilityToApply };
    }

    // Use IndexedDB streaming to update segments in batches
    const batchSize = 500;
    const selectedSet = new Set(selectedSegmentIds);
    let totalProcessed = 0;

    try {
        // Get total segment count for progress tracking
        const metadata = await indexedDBService.getSegmentMetadata('current-segments');
        const totalSegments = metadata?.totalSegments || 0;

        // Get all segments that need updating using streaming
        const segmentsToUpdate = await indexedDBService.getSegmentsByIds(selectedSegmentIds);

        if (segmentsToUpdate.length === 0) {
            console.log('No segments found to update');
            return;
        }

        // Update the segments
        const updatedSegments = segmentsToUpdate.map(segment => {
            const existingValue = segment.parameters?.[activeParameter.id];
            const needsUpdate = !existingValue ||
                               existingValue.value !== paramValue.value ||
                               existingValue.vulnerability !== paramValue.vulnerability;

            if (!needsUpdate) {
                return segment; // No change needed
            }

            totalProcessed++;

            // Update both direct parameters and properties.parameters
            const updatedDirectParameters = { ...segment.parameters, [activeParameter.id]: paramValue };
            const currentProperties = segment.properties || { id: segment.id };
            const currentPropertiesParams = currentProperties.parameters && typeof currentProperties.parameters === 'object'
                                           ? currentProperties.parameters
                                           : {};
            const updatedProperties = {
                ...currentProperties,
                parameters: { ...currentPropertiesParams, [activeParameter.id]: paramValue }
            };

            return { ...segment, parameters: updatedDirectParameters, properties: updatedProperties };
        });

        // Store the updated segments back to IndexedDB
        if (totalProcessed > 0) {
            // Get the current data and update it
            const currentData = await indexedDBService.getShorelineData('current-segments');
            if (currentData?.features) {
                // Create a map of updated segments for efficient lookup
                const updatedSegmentMap = new Map(updatedSegments.map(seg => [seg.id, seg]));

                // Update the features array
                const updatedFeatures = currentData.features.map(feature => {
                    const segmentId = feature.properties?.id;
                    const updatedSegment = segmentId ? updatedSegmentMap.get(segmentId) : null;

                    if (updatedSegment) {
                        return {
                            type: 'Feature' as const,
                            geometry: updatedSegment.geometry,
                            properties: updatedSegment.properties
                        };
                    }
                    return feature;
                });

                // Store the updated data
                const updatedGeoJSON = {
                    type: 'FeatureCollection' as const,
                    features: updatedFeatures
                };

                await indexedDBService.storeShorelineData('current-segments', updatedGeoJSON);
            }
        }

        console.log(`Streaming assignment completed: ${totalProcessed} segments updated`);
    } catch (error) {
        console.error('Streaming parameter assignment failed:', error);
        throw new Error(`Failed to apply parameter values: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

// Helper function to calculate bounds from segments
function calculateSegmentBounds(segments: ShorelineSegment[]): [[number, number], [number, number]] | null {
    if (segments.length === 0) return null;

    let minLat = Infinity, minLng = Infinity;
    let maxLat = -Infinity, maxLng = -Infinity;

    for (const segment of segments) {
        try {
            const bbox = turf.bbox(segment);
            const [west, south, east, north] = bbox;

            minLng = Math.min(minLng, west);
            minLat = Math.min(minLat, south);
            maxLng = Math.max(maxLng, east);
            maxLat = Math.max(maxLat, north);
        } catch (error) {
            console.warn('Error calculating bounds for segment:', segment.id, error);
        }
    }

    if (minLat === Infinity) return null;
    return [[minLat, minLng], [maxLat, maxLng]];
}
