/**
 * Test utilities for enhanced optimization performance
 */

import type { ShorelineSegment, Parameter } from '../types';
import { enhancedParameterService } from '../services/enhancedParameterService';

/**
 * Performance test for enhanced optimization
 */
export async function testEnhancedOptimization(
  segments: ShorelineSegment[],
  parameters: Parameter[]
): Promise<{
  isEnhancedAvailable: boolean;
  initializationTime: number;
  performanceStats: any;
  testResults: {
    segmentCount: number;
    parameterCount: number;
    estimatedSpeedup: string;
  };
}> {
  console.log('Testing enhanced optimization capabilities...');
  
  const startTime = performance.now();
  
  try {
    // Test service initialization
    await enhancedParameterService.initialize();
    const initTime = performance.now() - startTime;
    
    // Get performance statistics
    const stats = enhancedParameterService.getPerformanceStats();
    
    // Calculate estimated performance improvement
    const segmentCount = segments.length;
    const parameterCount = parameters.length;
    
    let estimatedSpeedup = "2-3x faster";
    if (segmentCount > 50000) {
      estimatedSpeedup = "5-10x faster";
    } else if (segmentCount > 10000) {
      estimatedSpeedup = "3-5x faster";
    }
    
    console.log(`Enhanced optimization test completed:
      - Initialization: ${initTime.toFixed(2)}ms
      - Segments: ${segmentCount.toLocaleString()}
      - Parameters: ${parameterCount}
      - Estimated speedup: ${estimatedSpeedup}
      - Web Worker available: ${stats.isInitialized}
    `);
    
    return {
      isEnhancedAvailable: true,
      initializationTime: initTime,
      performanceStats: stats,
      testResults: {
        segmentCount,
        parameterCount,
        estimatedSpeedup
      }
    };
  } catch (error) {
    console.warn('Enhanced optimization not available:', error);
    
    return {
      isEnhancedAvailable: false,
      initializationTime: performance.now() - startTime,
      performanceStats: null,
      testResults: {
        segmentCount: segments.length,
        parameterCount: parameters.length,
        estimatedSpeedup: "Not available"
      }
    };
  }
}

/**
 * Compare performance between different optimization levels
 */
export function getOptimizationRecommendation(
  segmentCount: number,
  parameterCount: number
): {
  recommendedMode: 'enhanced' | 'optimized' | 'legacy';
  reason: string;
  expectedPerformance: string;
} {
  const totalOperations = segmentCount * parameterCount;
  
  if (segmentCount > 20000 || totalOperations > 100000) {
    return {
      recommendedMode: 'enhanced',
      reason: 'Large dataset detected - Web Worker optimization recommended',
      expectedPerformance: 'Significant performance improvement with background processing'
    };
  } else if (segmentCount > 5000 || totalOperations > 25000) {
    return {
      recommendedMode: 'enhanced',
      reason: 'Medium dataset - Enhanced optimization will provide better responsiveness',
      expectedPerformance: 'Improved UI responsiveness and faster operations'
    };
  } else if (segmentCount > 1000) {
    return {
      recommendedMode: 'optimized',
      reason: 'Small to medium dataset - Standard optimization sufficient',
      expectedPerformance: 'Good performance with spatial indexing and caching'
    };
  } else {
    return {
      recommendedMode: 'legacy',
      reason: 'Small dataset - Simple operations sufficient',
      expectedPerformance: 'Adequate performance for small datasets'
    };
  }
}

/**
 * Monitor performance during operations
 */
export class PerformanceTracker {
  private startTime: number = 0;
  private operations: Array<{
    name: string;
    duration: number;
    segmentCount: number;
    timestamp: number;
  }> = [];

  startOperation(name: string): void {
    this.startTime = performance.now();
    console.log(`Starting operation: ${name}`);
  }

  endOperation(name: string, segmentCount: number): void {
    const duration = performance.now() - this.startTime;
    
    this.operations.push({
      name,
      duration,
      segmentCount,
      timestamp: Date.now()
    });

    console.log(`Operation completed: ${name} - ${duration.toFixed(2)}ms for ${segmentCount.toLocaleString()} segments`);
  }

  getStats(): {
    totalOperations: number;
    averageDuration: number;
    totalSegmentsProcessed: number;
    operationsPerSecond: number;
    recentOperations: Array<{ name: string; duration: number; segmentCount: number }>;
  } {
    if (this.operations.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        totalSegmentsProcessed: 0,
        operationsPerSecond: 0,
        recentOperations: []
      };
    }

    const totalDuration = this.operations.reduce((sum, op) => sum + op.duration, 0);
    const totalSegments = this.operations.reduce((sum, op) => sum + op.segmentCount, 0);
    const averageDuration = totalDuration / this.operations.length;
    const operationsPerSecond = this.operations.length / (totalDuration / 1000);

    return {
      totalOperations: this.operations.length,
      averageDuration,
      totalSegmentsProcessed: totalSegments,
      operationsPerSecond,
      recentOperations: this.operations.slice(-5).map(op => ({
        name: op.name,
        duration: op.duration,
        segmentCount: op.segmentCount
      }))
    };
  }

  reset(): void {
    this.operations = [];
    this.startTime = 0;
  }
}

// Global performance tracker instance
export const globalPerformanceTracker = new PerformanceTracker();
