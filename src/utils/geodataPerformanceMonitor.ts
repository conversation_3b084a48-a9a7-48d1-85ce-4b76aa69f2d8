/**
 * Performance monitoring specifically for geodata processing
 * Tracks coordinate validation, distance calculations, and processing bottlenecks
 */

export interface GeodataPerformanceMetrics {
  coordinateValidationTime: number;
  distanceCalculationTime: number;
  memoryUsage: {
    peak: number;
    average: number;
    current: number;
  };
  cachePerformance: {
    hitRate: number;
    totalRequests: number;
    cacheSize: number;
  };
  processingRate: {
    coordinatesPerSecond: number;
    segmentsPerSecond: number;
  };
  errors: {
    invalidCoordinates: number;
    calculationErrors: number;
    memoryErrors: number;
  };
  timestamp: number;
}

export interface ProcessingSession {
  sessionId: string;
  startTime: number;
  endTime?: number;
  totalCoordinates: number;
  totalSegments: number;
  metrics: GeodataPerformanceMetrics;
}

class GeodataPerformanceMonitor {
  private currentSession: ProcessingSession | null = null;
  private sessions: ProcessingSession[] = [];
  private memoryReadings: number[] = [];
  private coordinateValidationStart: number = 0;
  private distanceCalculationStart: number = 0;
  private errorCounts = {
    invalidCoordinates: 0,
    calculationErrors: 0,
    memoryErrors: 0
  };

  /**
   * Start a new performance monitoring session
   */
  startSession(sessionId: string): void {
    if (this.currentSession) {
      console.warn('Previous session not ended, forcing end');
      this.endSession();
    }

    this.currentSession = {
      sessionId,
      startTime: performance.now(),
      totalCoordinates: 0,
      totalSegments: 0,
      metrics: {
        coordinateValidationTime: 0,
        distanceCalculationTime: 0,
        memoryUsage: { peak: 0, average: 0, current: 0 },
        cachePerformance: { hitRate: 0, totalRequests: 0, cacheSize: 0 },
        processingRate: { coordinatesPerSecond: 0, segmentsPerSecond: 0 },
        errors: { invalidCoordinates: 0, calculationErrors: 0, memoryErrors: 0 },
        timestamp: Date.now()
      }
    };

    this.memoryReadings = [];
    this.errorCounts = { invalidCoordinates: 0, calculationErrors: 0, memoryErrors: 0 };
    this.startMemoryMonitoring();

    console.log(`Geodata performance monitoring started: ${sessionId}`);
  }

  /**
   * Start timing coordinate validation
   */
  startCoordinateValidation(): void {
    this.coordinateValidationStart = performance.now();
  }

  /**
   * End timing coordinate validation
   */
  endCoordinateValidation(): void {
    if (this.currentSession && this.coordinateValidationStart > 0) {
      const duration = performance.now() - this.coordinateValidationStart;
      this.currentSession.metrics.coordinateValidationTime += duration;
      this.coordinateValidationStart = 0;
    }
  }

  /**
   * Start timing distance calculation
   */
  startDistanceCalculation(): void {
    this.distanceCalculationStart = performance.now();
  }

  /**
   * End timing distance calculation
   */
  endDistanceCalculation(): void {
    if (this.currentSession && this.distanceCalculationStart > 0) {
      const duration = performance.now() - this.distanceCalculationStart;
      this.currentSession.metrics.distanceCalculationTime += duration;
      this.distanceCalculationStart = 0;
    }
  }

  /**
   * Record coordinate processing
   */
  recordCoordinateProcessing(count: number): void {
    if (this.currentSession) {
      this.currentSession.totalCoordinates += count;
    }
  }

  /**
   * Record segment processing
   */
  recordSegmentProcessing(count: number): void {
    if (this.currentSession) {
      this.currentSession.totalSegments += count;
    }
  }

  /**
   * Record an error
   */
  recordError(type: 'invalidCoordinates' | 'calculationErrors' | 'memoryErrors'): void {
    this.errorCounts[type]++;
    if (this.currentSession) {
      this.currentSession.metrics.errors[type]++;
    }
  }

  /**
   * Update cache performance metrics
   */
  updateCacheMetrics(hitRate: number, totalRequests: number, cacheSize: number): void {
    if (this.currentSession) {
      this.currentSession.metrics.cachePerformance = {
        hitRate,
        totalRequests,
        cacheSize
      };
    }
  }

  /**
   * End the current session and calculate final metrics
   */
  endSession(): GeodataPerformanceMetrics | null {
    if (!this.currentSession) {
      console.warn('No active session to end');
      return null;
    }

    const endTime = performance.now();
    this.currentSession.endTime = endTime;
    
    const totalTime = (endTime - this.currentSession.startTime) / 1000; // Convert to seconds

    // Calculate processing rates
    this.currentSession.metrics.processingRate = {
      coordinatesPerSecond: this.currentSession.totalCoordinates / totalTime,
      segmentsPerSecond: this.currentSession.totalSegments / totalTime
    };

    // Calculate memory statistics
    if (this.memoryReadings.length > 0) {
      this.currentSession.metrics.memoryUsage = {
        peak: Math.max(...this.memoryReadings),
        average: this.memoryReadings.reduce((sum, val) => sum + val, 0) / this.memoryReadings.length,
        current: this.getCurrentMemoryUsage()
      };
    }

    // Store session
    this.sessions.push({ ...this.currentSession });
    
    // Keep only last 10 sessions
    if (this.sessions.length > 10) {
      this.sessions = this.sessions.slice(-10);
    }

    const metrics = this.currentSession.metrics;
    this.currentSession = null;

    console.log('Geodata performance session ended:', {
      totalTime: totalTime.toFixed(2) + 's',
      coordinatesPerSecond: metrics.processingRate.coordinatesPerSecond.toFixed(0),
      segmentsPerSecond: metrics.processingRate.segmentsPerSecond.toFixed(0),
      validationTime: metrics.coordinateValidationTime.toFixed(2) + 'ms',
      calculationTime: metrics.distanceCalculationTime.toFixed(2) + 'ms',
      errors: metrics.errors
    });

    return metrics;
  }

  /**
   * Get performance statistics across all sessions
   */
  getPerformanceStats(): {
    sessionCount: number;
    averageCoordinatesPerSecond: number;
    averageSegmentsPerSecond: number;
    averageValidationTime: number;
    averageCalculationTime: number;
    totalErrors: { invalidCoordinates: number; calculationErrors: number; memoryErrors: number };
  } {
    if (this.sessions.length === 0) {
      return {
        sessionCount: 0,
        averageCoordinatesPerSecond: 0,
        averageSegmentsPerSecond: 0,
        averageValidationTime: 0,
        averageCalculationTime: 0,
        totalErrors: { invalidCoordinates: 0, calculationErrors: 0, memoryErrors: 0 }
      };
    }

    const totalErrors = this.sessions.reduce((acc, session) => ({
      invalidCoordinates: acc.invalidCoordinates + session.metrics.errors.invalidCoordinates,
      calculationErrors: acc.calculationErrors + session.metrics.errors.calculationErrors,
      memoryErrors: acc.memoryErrors + session.metrics.errors.memoryErrors
    }), { invalidCoordinates: 0, calculationErrors: 0, memoryErrors: 0 });

    return {
      sessionCount: this.sessions.length,
      averageCoordinatesPerSecond: this.sessions.reduce((sum, s) => sum + s.metrics.processingRate.coordinatesPerSecond, 0) / this.sessions.length,
      averageSegmentsPerSecond: this.sessions.reduce((sum, s) => sum + s.metrics.processingRate.segmentsPerSecond, 0) / this.sessions.length,
      averageValidationTime: this.sessions.reduce((sum, s) => sum + s.metrics.coordinateValidationTime, 0) / this.sessions.length,
      averageCalculationTime: this.sessions.reduce((sum, s) => sum + s.metrics.distanceCalculationTime, 0) / this.sessions.length,
      totalErrors
    };
  }

  /**
   * Get current session metrics
   */
  getCurrentSessionMetrics(): GeodataPerformanceMetrics | null {
    return this.currentSession?.metrics || null;
  }

  /**
   * Check if system is under performance stress
   */
  isPerformanceStressed(): boolean {
    const stats = this.getPerformanceStats();
    const currentMemory = this.getCurrentMemoryUsage();
    
    return (
      stats.averageCoordinatesPerSecond < 1000 || // Very slow coordinate processing
      stats.totalErrors.invalidCoordinates > stats.sessionCount * 100 || // Too many coordinate errors
      currentMemory > 0.9 // High memory usage
    );
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    const recordMemory = () => {
      const usage = this.getCurrentMemoryUsage();
      if (usage > 0) {
        this.memoryReadings.push(usage);
      }
    };

    // Record memory every 500ms during processing
    const memoryInterval = setInterval(recordMemory, 500);
    
    // Store interval ID for cleanup
    if (this.currentSession) {
      (this.currentSession as any).memoryInterval = memoryInterval;
    }
  }

  /**
   * Get current memory usage as percentage
   */
  private getCurrentMemoryUsage(): number {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      return memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
    }
    return 0;
  }
}

// Global instance
export const geodataPerformanceMonitor = new GeodataPerformanceMonitor();
