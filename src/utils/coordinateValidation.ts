/**
 * Coordinate System Validation and Transformation Utilities
 * Handles CRS detection, validation, and transformation for geodata processing
 */

import type { FeatureCollection, Feature, Geometry, Position } from 'geojson';

// Coordinate system constants
export const WGS84_EPSG = 'EPSG:4326';
export const WEB_MERCATOR_EPSG = 'EPSG:3857';

// Coordinate bounds for different systems
export const WGS84_BOUNDS = {
  minX: -180.0,
  maxX: 180.0,
  minY: -90.0,
  maxY: 90.0
};

export const WEB_MERCATOR_BOUNDS = {
  minX: -20037508.342789244,
  maxX: 20037508.342789244,
  minY: -20037508.342789244,
  maxY: 20037508.342789244
};

/**
 * Coordinate system detection result
 */
export interface CoordinateSystemInfo {
  epsg: string;
  name: string;
  confidence: number;
  bounds: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
}

/**
 * Validates if coordinates are within WGS84 bounds
 */
export function isValidWGS84Coordinate(lon: number, lat: number): boolean {
  return (
    typeof lon === 'number' &&
    typeof lat === 'number' &&
    !isNaN(lon) &&
    !isNaN(lat) &&
    isFinite(lon) &&
    isFinite(lat) &&
    lon >= WGS84_BOUNDS.minX &&
    lon <= WGS84_BOUNDS.maxX &&
    lat >= WGS84_BOUNDS.minY &&
    lat <= WGS84_BOUNDS.maxY
  );
}

/**
 * Validates if coordinates are within Web Mercator bounds
 */
export function isValidWebMercatorCoordinate(x: number, y: number): boolean {
  return (
    typeof x === 'number' &&
    typeof y === 'number' &&
    !isNaN(x) &&
    !isNaN(y) &&
    isFinite(x) &&
    isFinite(y) &&
    x >= WEB_MERCATOR_BOUNDS.minX &&
    x <= WEB_MERCATOR_BOUNDS.maxX &&
    y >= WEB_MERCATOR_BOUNDS.minY &&
    y <= WEB_MERCATOR_BOUNDS.maxY
  );
}

/**
 * Detects coordinate system from sample coordinates
 */
export function detectCoordinateSystem(coordinates: Position[]): CoordinateSystemInfo {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('No coordinates provided for CRS detection');
  }

  let wgs84Count = 0;
  let webMercatorCount = 0;
  let totalCount = 0;

  // Sample up to 100 coordinates for detection
  const sampleSize = Math.min(coordinates.length, 100);
  const step = Math.max(1, Math.floor(coordinates.length / sampleSize));

  for (let i = 0; i < coordinates.length; i += step) {
    const coord = coordinates[i];
    if (!Array.isArray(coord) || coord.length < 2) continue;

    const [x, y] = coord;
    totalCount++;

    if (isValidWGS84Coordinate(x, y)) {
      wgs84Count++;
    }
    if (isValidWebMercatorCoordinate(x, y)) {
      webMercatorCount++;
    }
  }

  if (totalCount === 0) {
    throw new Error('No valid coordinates found for CRS detection');
  }

  const wgs84Confidence = wgs84Count / totalCount;
  const webMercatorConfidence = webMercatorCount / totalCount;

  // Determine most likely coordinate system
  if (wgs84Confidence > 0.8) {
    return {
      epsg: WGS84_EPSG,
      name: 'WGS84 Geographic',
      confidence: wgs84Confidence,
      bounds: WGS84_BOUNDS
    };
  } else if (webMercatorConfidence > 0.8) {
    return {
      epsg: WEB_MERCATOR_EPSG,
      name: 'Web Mercator',
      confidence: webMercatorConfidence,
      bounds: WEB_MERCATOR_BOUNDS
    };
  } else {
    // Default to WGS84 with low confidence
    return {
      epsg: WGS84_EPSG,
      name: 'WGS84 Geographic (assumed)',
      confidence: Math.max(wgs84Confidence, 0.1),
      bounds: WGS84_BOUNDS
    };
  }
}

/**
 * Extracts all coordinates from a GeoJSON geometry
 */
function extractCoordinatesFromGeometry(geometry: Geometry): Position[] {
  const coordinates: Position[] = [];

  function extractFromCoords(coords: any): void {
    if (Array.isArray(coords)) {
      if (coords.length >= 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
        // This is a coordinate pair
        coordinates.push(coords as Position);
      } else {
        // This is an array of coordinates or nested arrays
        coords.forEach(extractFromCoords);
      }
    }
  }

  // Handle different geometry types
  if (geometry.type === 'GeometryCollection') {
    // For GeometryCollection, recursively extract from each geometry
    geometry.geometries.forEach(geom => {
      coordinates.push(...extractCoordinatesFromGeometry(geom));
    });
  } else if ('coordinates' in geometry) {
    // For other geometry types, extract from coordinates property
    extractFromCoords(geometry.coordinates);
  }

  return coordinates;
}

/**
 * Detects coordinate system from GeoJSON data
 */
export function detectGeoJSONCoordinateSystem(geoJSON: FeatureCollection | Feature): CoordinateSystemInfo {
  const allCoordinates: Position[] = [];

  if (geoJSON.type === 'FeatureCollection') {
    for (const feature of geoJSON.features) {
      if (feature.geometry) {
        allCoordinates.push(...extractCoordinatesFromGeometry(feature.geometry));
      }
    }
  } else if (geoJSON.type === 'Feature' && geoJSON.geometry) {
    allCoordinates.push(...extractCoordinatesFromGeometry(geoJSON.geometry));
  }

  if (allCoordinates.length === 0) {
    throw new Error('No coordinates found in GeoJSON for CRS detection');
  }

  return detectCoordinateSystem(allCoordinates);
}

/**
 * Validates that all coordinates in a GeoJSON are valid for the detected CRS
 */
export function validateGeoJSONCoordinates(geoJSON: FeatureCollection | Feature): {
  isValid: boolean;
  crs: CoordinateSystemInfo;
  invalidCount: number;
  totalCount: number;
  errors: string[];
} {
  const errors: string[] = [];
  let invalidCount = 0;
  let totalCount = 0;

  try {
    const crs = detectGeoJSONCoordinateSystem(geoJSON);
    const allCoordinates = [];

    if (geoJSON.type === 'FeatureCollection') {
      for (const feature of geoJSON.features) {
        if (feature.geometry) {
          allCoordinates.push(...extractCoordinatesFromGeometry(feature.geometry));
        }
      }
    } else if (geoJSON.type === 'Feature' && geoJSON.geometry) {
      allCoordinates.push(...extractCoordinatesFromGeometry(geoJSON.geometry));
    }

    totalCount = allCoordinates.length;

    for (const coord of allCoordinates) {
      if (!Array.isArray(coord) || coord.length < 2) {
        invalidCount++;
        continue;
      }

      const [x, y] = coord;
      let isValid = false;

      if (crs.epsg === WGS84_EPSG) {
        isValid = isValidWGS84Coordinate(x, y);
      } else if (crs.epsg === WEB_MERCATOR_EPSG) {
        isValid = isValidWebMercatorCoordinate(x, y);
      }

      if (!isValid) {
        invalidCount++;
      }
    }

    const isValid = invalidCount === 0;
    if (!isValid) {
      errors.push(`${invalidCount} out of ${totalCount} coordinates are invalid for ${crs.name}`);
    }

    return {
      isValid,
      crs,
      invalidCount,
      totalCount,
      errors
    };

  } catch (error) {
    errors.push(`CRS validation failed: ${error instanceof Error ? error.message : String(error)}`);
    return {
      isValid: false,
      crs: {
        epsg: WGS84_EPSG,
        name: 'Unknown',
        confidence: 0,
        bounds: WGS84_BOUNDS
      },
      invalidCount: 0,
      totalCount: 0,
      errors
    };
  }
}

/**
 * Normalizes longitude values to [-180, 180] range
 */
export function normalizeLongitude(lon: number): number {
  while (lon > 180) lon -= 360;
  while (lon < -180) lon += 360;
  return lon;
}

/**
 * Normalizes all longitude values in a coordinate array
 */
export function normalizeCoordinates(coordinates: Position[]): Position[] {
  return coordinates.map(coord => {
    if (!Array.isArray(coord) || coord.length < 2) {
      return coord;
    }
    const [lon, lat, ...rest] = coord;
    return [normalizeLongitude(lon), lat, ...rest] as Position;
  });
}
