/**
 * Optimized geodesic calculations with caching for CVIc segmentation
 * Replaces simple coordinate distance with proper geodesic formulas
 * Includes coordinate validation and precision handling
 */

// Earth radius in meters (WGS84 semi-major axis)
const EARTH_RADIUS = 6378137.0; // More precise WGS84 value

// Coordinate validation constants
const MIN_LONGITUDE = -180.0;
const MAX_LONGITUDE = 180.0;
const MIN_LATITUDE = -90.0;
const MAX_LATITUDE = 90.0;
const COORDINATE_PRECISION = 1e-9; // Minimum meaningful coordinate difference (~0.1mm)

/**
 * Validates coordinate values for geodesic calculations
 */
function validateCoordinate(lat: number, lon: number): boolean {
  return (
    typeof lat === 'number' &&
    typeof lon === 'number' &&
    !isNaN(lat) &&
    !isNaN(lon) &&
    isFinite(lat) &&
    isFinite(lon) &&
    lat >= MIN_LATITUDE &&
    lat <= MAX_LATITUDE &&
    lon >= MIN_LONGITUDE &&
    lon <= MAX_LONGITUDE
  );
}

/**
 * Normalizes longitude to [-180, 180] range
 */
function normalizeLongitude(lon: number): number {
  while (lon > MAX_LONGITUDE) lon -= 360;
  while (lon < MIN_LONGITUDE) lon += 360;
  return lon;
}

/**
 * Checks if coordinate difference is meaningful for distance calculation
 */
function isCoordinateDifferenceSignificant(lat1: number, lon1: number, lat2: number, lon2: number): boolean {
  return (
    Math.abs(lat2 - lat1) > COORDINATE_PRECISION ||
    Math.abs(lon2 - lon1) > COORDINATE_PRECISION
  );
}

// LRU Cache for distance calculations
class DistanceCache {
  private cache = new Map<string, number>();
  private maxSize = 10000;
  private hitCount = 0;
  private missCount = 0;

  private getCacheKey(lat1: number, lon1: number, lat2: number, lon2: number): string {
    // Validate coordinates before caching
    if (!validateCoordinate(lat1, lon1) || !validateCoordinate(lat2, lon2)) {
      throw new Error(`Invalid coordinates for caching: (${lat1}, ${lon1}) to (${lat2}, ${lon2})`);
    }

    // Round to 7 decimal places for cache key (≈0.01m precision)
    const roundedLat1 = Math.round(lat1 * 10000000) / 10000000;
    const roundedLon1 = Math.round(lon1 * 10000000) / 10000000;
    const roundedLat2 = Math.round(lat2 * 10000000) / 10000000;
    const roundedLon2 = Math.round(lon2 * 10000000) / 10000000;
    return `${roundedLat1},${roundedLon1},${roundedLat2},${roundedLon2}`;
  }

  get(lat1: number, lon1: number, lat2: number, lon2: number): number | undefined {
    const key = this.getCacheKey(lat1, lon1, lat2, lon2);
    const cached = this.cache.get(key);
    if (cached !== undefined) {
      this.hitCount++;
      // Move to end (LRU)
      this.cache.delete(key);
      this.cache.set(key, cached);
      return cached;
    }
    this.missCount++;
    return undefined;
  }

  set(lat1: number, lon1: number, lat2: number, lon2: number, distance: number): void {
    const key = this.getCacheKey(lat1, lon1, lat2, lon2);

    // Remove oldest if at capacity
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, distance);
  }

  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }

  getStats(): { size: number; maxSize: number; hitRate: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    };
  }
}

// Global cache instance
const distanceCache = new DistanceCache();

/**
 * Fast approximation of geodesic distance using equirectangular projection
 * Good for short distances, much faster than Haversine
 * Includes coordinate validation and normalization
 */
export function fastGeodesicDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  // Validate input coordinates
  if (!validateCoordinate(lat1, lon1) || !validateCoordinate(lat2, lon2)) {
    throw new Error(`Invalid coordinates for distance calculation: (${lat1}, ${lon1}) to (${lat2}, ${lon2})`);
  }

  // Normalize longitudes to handle dateline crossing
  const normalizedLon1 = normalizeLongitude(lon1);
  const normalizedLon2 = normalizeLongitude(lon2);

  // Check if coordinates are effectively the same
  if (!isCoordinateDifferenceSignificant(lat1, normalizedLon1, lat2, normalizedLon2)) {
    return 0.0;
  }

  // Check cache first
  const cached = distanceCache.get(lat1, normalizedLon1, lat2, normalizedLon2);
  if (cached !== undefined) {
    return cached;
  }

  // Handle dateline crossing for longitude difference
  let dLon = normalizedLon2 - normalizedLon1;
  if (Math.abs(dLon) > 180) {
    dLon = dLon > 0 ? dLon - 360 : dLon + 360;
  }

  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLonRad = dLon * Math.PI / 180;
  const avgLat = (lat1 + lat2) / 2 * Math.PI / 180;

  const x = dLonRad * Math.cos(avgLat);
  const distance = Math.sqrt(x * x + dLat * dLat) * EARTH_RADIUS;

  // Cache result
  distanceCache.set(lat1, normalizedLon1, lat2, normalizedLon2, distance);

  return distance;
}

/**
 * Precise Haversine distance calculation for high accuracy needs
 * Includes coordinate validation and proper dateline handling
 */
export function haversineDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  // Validate input coordinates
  if (!validateCoordinate(lat1, lon1) || !validateCoordinate(lat2, lon2)) {
    throw new Error(`Invalid coordinates for Haversine calculation: (${lat1}, ${lon1}) to (${lat2}, ${lon2})`);
  }

  // Normalize longitudes to handle dateline crossing
  const normalizedLon1 = normalizeLongitude(lon1);
  const normalizedLon2 = normalizeLongitude(lon2);

  // Check if coordinates are effectively the same
  if (!isCoordinateDifferenceSignificant(lat1, normalizedLon1, lat2, normalizedLon2)) {
    return 0.0;
  }

  // Check cache first
  const cached = distanceCache.get(lat1, normalizedLon1, lat2, normalizedLon2);
  if (cached !== undefined) {
    return cached;
  }

  // Handle dateline crossing for longitude difference
  let dLon = normalizedLon2 - normalizedLon1;
  if (Math.abs(dLon) > 180) {
    dLon = dLon > 0 ? dLon - 360 : dLon + 360;
  }

  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLonRad = dLon * Math.PI / 180;
  const lat1Rad = lat1 * Math.PI / 180;
  const lat2Rad = lat2 * Math.PI / 180;

  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1Rad) * Math.cos(lat2Rad) *
            Math.sin(dLonRad / 2) * Math.sin(dLonRad / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = EARTH_RADIUS * c;

  // Cache result
  distanceCache.set(lat1, normalizedLon1, lat2, normalizedLon2, distance);

  return distance;
}

/**
 * Optimized distance calculation that chooses algorithm based on distance
 * Includes coordinate validation and intelligent algorithm selection
 */
export function optimizedGeodesicDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  // Validate input coordinates first
  if (!validateCoordinate(lat1, lon1) || !validateCoordinate(lat2, lon2)) {
    throw new Error(`Invalid coordinates for optimized distance calculation: (${lat1}, ${lon1}) to (${lat2}, ${lon2})`);
  }

  // Normalize longitudes
  const normalizedLon1 = normalizeLongitude(lon1);
  const normalizedLon2 = normalizeLongitude(lon2);

  // Check if coordinates are effectively the same
  if (!isCoordinateDifferenceSignificant(lat1, normalizedLon1, lat2, normalizedLon2)) {
    return 0.0;
  }

  // Calculate rough distance for algorithm selection
  // Handle dateline crossing in rough calculation
  let dLon = Math.abs(normalizedLon2 - normalizedLon1);
  if (dLon > 180) {
    dLon = 360 - dLon;
  }
  const roughDistance = Math.abs(lat2 - lat1) + dLon;

  // For very short distances (< ~1km), use fast approximation
  // For longer distances, use precise Haversine
  if (roughDistance < 0.01) {
    return fastGeodesicDistance(lat1, normalizedLon1, lat2, normalizedLon2);
  } else {
    return haversineDistance(lat1, normalizedLon1, lat2, normalizedLon2);
  }
}

/**
 * Batch distance calculation for arrays of coordinates
 * Includes comprehensive validation and error handling
 */
export function batchGeodesicDistances(coordinates: [number, number][]): number[] {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid coordinates array for batch distance calculation');
  }

  if (coordinates.length === 1) {
    return [0];
  }

  const distances = new Array(coordinates.length);
  distances[0] = 0;

  try {
    for (let i = 1; i < coordinates.length; i++) {
      const [lon1, lat1] = coordinates[i - 1];
      const [lon2, lat2] = coordinates[i];

      // Validate coordinate pairs
      if (!Array.isArray(coordinates[i - 1]) || coordinates[i - 1].length < 2 ||
          !Array.isArray(coordinates[i]) || coordinates[i].length < 2) {
        throw new Error(`Invalid coordinate format at index ${i - 1} or ${i}`);
      }

      const segmentDistance = optimizedGeodesicDistance(lat1, lon1, lat2, lon2);
      distances[i] = distances[i - 1] + segmentDistance;
    }
  } catch (error) {
    throw new Error(`Batch distance calculation failed: ${error instanceof Error ? error.message : String(error)}`);
  }

  return distances;
}

/**
 * Clear distance cache (useful for memory management)
 */
export function clearDistanceCache(): void {
  distanceCache.clear();
}

/**
 * Get cache statistics for monitoring
 */
export function getDistanceCacheStats() {
  return distanceCache.getStats();
}
