/**
 * Memory pool management for efficient coordinate and segment handling
 * Reduces garbage collection pressure and improves performance
 * Includes coordinate validation and error handling
 */

import { isValidWGS84Coordinate } from './coordinateValidation';

// TypedArray pools for coordinate storage
class TypedArrayPool<T extends ArrayBufferView> {
  private pool: T[] = [];
  private createFn: (size: number) => T;
  private maxPoolSize: number;
  private static readonly MAX_ARRAY_SIZE = 2147483647; // Max safe array size

  constructor(createFn: (size: number) => T, maxPoolSize = 100) {
    this.createFn = createFn;
    this.maxPoolSize = maxPoolSize;
  }

  acquire(size: number): T {
    // Validate array size before creation
    if (size > TypedArrayPool.MAX_ARRAY_SIZE) {
      throw new Error(`Requested array size ${size} exceeds maximum safe size ${TypedArrayPool.MAX_ARRAY_SIZE}`);
    }

    // Try to find a suitable array from the pool
    for (let i = 0; i < this.pool.length; i++) {
      const array = this.pool[i];
      if (array.length >= size) {
        this.pool.splice(i, 1);
        return array;
      }
    }

    // Create new array if none suitable found
    try {
      return this.createFn(size);
    } catch (error) {
      throw new Error(`Failed to create TypedArray of size ${size}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  release(array: T): void {
    if (this.pool.length < this.maxPoolSize) {
      // Clear the array before returning to pool
      if ('fill' in array) {
        (array as any).fill(0);
      }
      this.pool.push(array);
    }
  }

  clear(): void {
    this.pool.length = 0;
  }

  getStats(): { poolSize: number; maxPoolSize: number } {
    return {
      poolSize: this.pool.length,
      maxPoolSize: this.maxPoolSize
    };
  }
}

// Global pools for different data types
const float32Pool = new TypedArrayPool<Float32Array>((size) => new Float32Array(size));
const float64Pool = new TypedArrayPool<Float64Array>((size) => new Float64Array(size));
const uint32Pool = new TypedArrayPool<Uint32Array>((size) => new Uint32Array(size));

/**
 * Efficient coordinate storage using Float32Array with size limits
 */
export class CoordinateBuffer {
  private buffer: Float32Array;
  private length: number = 0;
  private static readonly MAX_ARRAY_SIZE = 2147483647; // Max safe array size
  private static readonly MAX_COORDINATES = Math.floor(CoordinateBuffer.MAX_ARRAY_SIZE / 2); // x,y pairs

  constructor(initialCapacity = 1000) {
    // Ensure we don't exceed maximum array size
    const safeCapacity = Math.min(initialCapacity, CoordinateBuffer.MAX_COORDINATES);
    this.buffer = float32Pool.acquire(safeCapacity * 2); // x,y pairs
  }

  addCoordinate(x: number, y: number): void {
    // Validate coordinate values
    if (!isValidWGS84Coordinate(x, y)) {
      throw new Error(`Invalid coordinate values: (${x}, ${y}). Must be valid WGS84 coordinates.`);
    }

    // Check if we're approaching array size limits
    if (this.length >= CoordinateBuffer.MAX_COORDINATES) {
      throw new Error(`Coordinate buffer size limit exceeded. Maximum ${CoordinateBuffer.MAX_COORDINATES} coordinates supported.`);
    }

    if (this.length * 2 >= this.buffer.length) {
      this.resize();
    }

    this.buffer[this.length * 2] = x;
    this.buffer[this.length * 2 + 1] = y;
    this.length++;
  }

  getCoordinate(index: number): [number, number] {
    if (index >= this.length) {
      throw new Error('Index out of bounds');
    }
    return [this.buffer[index * 2], this.buffer[index * 2 + 1]];
  }

  getCoordinates(): Float32Array {
    return this.buffer.subarray(0, this.length * 2);
  }

  getLength(): number {
    return this.length;
  }

  clear(): void {
    this.length = 0;
  }

  private resize(): void {
    // Calculate new size but respect maximum limits
    const currentSize = this.buffer.length;
    const requestedSize = currentSize * 2;
    const maxAllowedSize = CoordinateBuffer.MAX_COORDINATES * 2;

    if (currentSize >= maxAllowedSize) {
      throw new Error(`Cannot resize coordinate buffer beyond maximum size of ${maxAllowedSize} elements.`);
    }

    const newSize = Math.min(requestedSize, maxAllowedSize);
    const newBuffer = float32Pool.acquire(newSize);

    try {
      newBuffer.set(this.buffer);
      float32Pool.release(this.buffer);
      this.buffer = newBuffer;
    } catch (error) {
      // If set() fails due to size issues, fall back to manual copying
      console.warn('Array.set() failed, using manual copy:', error);
      for (let i = 0; i < this.buffer.length; i++) {
        newBuffer[i] = this.buffer[i];
      }
      float32Pool.release(this.buffer);
      this.buffer = newBuffer;
    }
  }

  dispose(): void {
    float32Pool.release(this.buffer);
  }

  // Static method to check if a coordinate count is safe
  static isSafeSize(coordinateCount: number): boolean {
    return coordinateCount <= CoordinateBuffer.MAX_COORDINATES;
  }
}

/**
 * Efficient segment metadata storage with size limits
 */
export class SegmentMetadataBuffer {
  private idBuffer: Uint32Array;
  private lengthBuffer: Float32Array;
  private indexBuffer: Uint32Array;
  private count: number = 0;
  private static readonly MAX_SEGMENTS = 2147483647; // Max safe array size

  constructor(initialCapacity = 1000) {
    // Ensure we don't exceed maximum array size
    const safeCapacity = Math.min(initialCapacity, SegmentMetadataBuffer.MAX_SEGMENTS);
    this.idBuffer = uint32Pool.acquire(safeCapacity);
    this.lengthBuffer = float32Pool.acquire(safeCapacity);
    this.indexBuffer = uint32Pool.acquire(safeCapacity);
  }

  addSegment(id: number, length: number, index: number): void {
    // Check if we're approaching array size limits
    if (this.count >= SegmentMetadataBuffer.MAX_SEGMENTS) {
      throw new Error(`Segment metadata buffer size limit exceeded. Maximum ${SegmentMetadataBuffer.MAX_SEGMENTS} segments supported.`);
    }

    if (this.count >= this.idBuffer.length) {
      this.resize();
    }

    this.idBuffer[this.count] = id;
    this.lengthBuffer[this.count] = length;
    this.indexBuffer[this.count] = index;
    this.count++;
  }

  getSegment(index: number): { id: number; length: number; segmentIndex: number } {
    if (index >= this.count) {
      throw new Error('Index out of bounds');
    }

    return {
      id: this.idBuffer[index],
      length: this.lengthBuffer[index],
      segmentIndex: this.indexBuffer[index]
    };
  }

  getCount(): number {
    return this.count;
  }

  clear(): void {
    this.count = 0;
  }

  private resize(): void {
    // Calculate new size but respect maximum limits
    const currentSize = this.idBuffer.length;
    const requestedSize = currentSize * 2;
    const maxAllowedSize = SegmentMetadataBuffer.MAX_SEGMENTS;

    if (currentSize >= maxAllowedSize) {
      throw new Error(`Cannot resize segment metadata buffer beyond maximum size of ${maxAllowedSize} elements.`);
    }

    const newSize = Math.min(requestedSize, maxAllowedSize);

    const newIdBuffer = uint32Pool.acquire(newSize);
    const newLengthBuffer = float32Pool.acquire(newSize);
    const newIndexBuffer = uint32Pool.acquire(newSize);

    try {
      newIdBuffer.set(this.idBuffer);
      newLengthBuffer.set(this.lengthBuffer);
      newIndexBuffer.set(this.indexBuffer);
    } catch (error) {
      // If set() fails due to size issues, fall back to manual copying
      console.warn('Array.set() failed in metadata buffer, using manual copy:', error);
      for (let i = 0; i < this.idBuffer.length; i++) {
        newIdBuffer[i] = this.idBuffer[i];
        newLengthBuffer[i] = this.lengthBuffer[i];
        newIndexBuffer[i] = this.indexBuffer[i];
      }
    }

    uint32Pool.release(this.idBuffer);
    float32Pool.release(this.lengthBuffer);
    uint32Pool.release(this.indexBuffer);

    this.idBuffer = newIdBuffer;
    this.lengthBuffer = newLengthBuffer;
    this.indexBuffer = newIndexBuffer;
  }

  dispose(): void {
    uint32Pool.release(this.idBuffer);
    float32Pool.release(this.lengthBuffer);
    uint32Pool.release(this.indexBuffer);
  }

  // Static method to check if a segment count is safe
  static isSafeSize(segmentCount: number): boolean {
    return segmentCount <= SegmentMetadataBuffer.MAX_SEGMENTS;
  }
}

/**
 * Memory pressure monitoring
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryInfo: any = null;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  getMemoryUsage(): { used: number; total: number; percentage: number } | null {
    if ('memory' in performance) {
      this.memoryInfo = (performance as any).memory;
      return {
        used: this.memoryInfo.usedJSHeapSize,
        total: this.memoryInfo.totalJSHeapSize,
        percentage: (this.memoryInfo.usedJSHeapSize / this.memoryInfo.totalJSHeapSize) * 100
      };
    }
    return null;
  }

  isMemoryPressureHigh(): boolean {
    const usage = this.getMemoryUsage();
    return usage ? usage.percentage > 80 : false;
  }

  forceGarbageCollection(): void {
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  }

  getCurrentMemoryUsage(): { used: number; total: number; percentage: number } | null {
    if ('memory' in performance) {
      this.memoryInfo = (performance as any).memory;
      return {
        used: this.memoryInfo.usedJSHeapSize,
        total: this.memoryInfo.totalJSHeapSize,
        percentage: (this.memoryInfo.usedJSHeapSize / this.memoryInfo.totalJSHeapSize) * 100
      };
    }
    return null;
  }
}

/**
 * Clear all memory pools
 */
export function clearAllPools(): void {
  float32Pool.clear();
  float64Pool.clear();
  uint32Pool.clear();
}

/**
 * Get memory pool statistics
 */
export function getPoolStats() {
  return {
    float32: float32Pool.getStats(),
    float64: float64Pool.getStats(),
    uint32: uint32Pool.getStats()
  };
}
