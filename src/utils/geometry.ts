import { FeatureCollection, LineString, MultiLineString, Position } from 'geojson'
import { ShorelineSegment } from '../types'
import { optimizedGeodesicDistance, batchGeodesicDistances, clearDistanceCache, getDistanceCacheStats } from './geodesic'
import { CoordinateBuffer, SegmentMetadataBuffer, MemoryMonitor, clearAllPools } from './memoryPool'
import { performanceMonitor } from '../services/performanceMonitor'

// Types for optimized segmentation
export interface SegmentationProgress {
  completed: number;
  total: number;
  percentage: number;
  currentFeature: number;
  totalFeatures: number;
}

export interface SegmentationOptions {
  onProgress?: (progress: SegmentationProgress) => void;
  abortSignal?: AbortSignal;
  batchSize?: number;
}



/**
 * Optimized segmentation with Web Worker support and performance monitoring
 * Uses advanced algorithms and memory management for maximum performance
 */
export async function segmentShoreline(
  shoreline: FeatureCollection<LineString | MultiLineString>,
  resolution: number,
  options: SegmentationOptions = {}
): Promise<ShorelineSegment[]> {
  const { onProgress, abortSignal, batchSize = 2000 } = options;

  // Start performance monitoring
  performanceMonitor.startSession();
  const memoryMonitor = MemoryMonitor.getInstance();

  // Get optimization recommendations
  const recommendations = performanceMonitor.getOptimizationRecommendations();
  const optimizedBatchSize = batchSize || recommendations.batchSize;

  // Create unique session ID for this segmentation run
  const sessionId = `current-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  console.log('Starting optimized segmentation with settings:', {
    sessionId,
    batchSize: optimizedBatchSize,
    useCompression: recommendations.useCompression,
    memoryThreshold: recommendations.memoryThreshold
  });

  // Clear caches and old segments for fresh start
  clearDistanceCache();
  clearAllPools();

  // Clear any existing segment chunks from previous runs
  const { indexedDBService } = await import('../services/indexedDBService');
  try {
    await indexedDBService.clearSegmentChunks(); // Clear all old chunks
    console.log('Cleared previous segment chunks');
  } catch (error) {
    console.warn('Failed to clear previous chunks:', error);
  }

  let segmentId = 1;
  let totalProcessed = 0;
  let chunkCounter = 0;

  // Calculate total work using optimized estimation
  const totalEstimatedSegments = calculateOptimizedEstimatedSegments(shoreline, resolution);
  const totalFeatures = shoreline.features.reduce((sum, f) =>
    sum + (f.geometry.type === 'MultiLineString' ? f.geometry.coordinates.length : 1), 0);

  console.log(`Optimized mode: ${totalEstimatedSegments} segments estimated, batch size: ${optimizedBatchSize}`);

  try {
    // Process each feature individually with optimized algorithms
    for (let featureIndex = 0; featureIndex < shoreline.features.length; featureIndex++) {
      if (abortSignal?.aborted) {
        throw new Error('Segmentation cancelled');
      }

      const feature = shoreline.features[featureIndex];
      const coordinateArrays = feature.geometry.type === 'MultiLineString'
        ? feature.geometry.coordinates
        : [feature.geometry.coordinates];

      // Process each line with optimized streaming
      for (let lineIndex = 0; lineIndex < coordinateArrays.length; lineIndex++) {
        if (abortSignal?.aborted) {
          throw new Error('Segmentation cancelled');
        }

        const coordinates = coordinateArrays[lineIndex] as Position[];

        // Check if this line is too large to process in one go
        const maxCoordinatesPerChunk = Math.floor(2147483647 / 4); // Safe limit for processing

        if (coordinates.length > maxCoordinatesPerChunk) {
          console.log(`Large line detected (${coordinates.length} coordinates), processing in chunks`);

          // Process line in chunks
          let lineProcessed = 0;
          const chunkSize = maxCoordinatesPerChunk;

          for (let chunkStart = 0; chunkStart < coordinates.length - 1; chunkStart += chunkSize - 1) {
            const chunkEnd = Math.min(chunkStart + chunkSize, coordinates.length);
            const chunkCoordinates = coordinates.slice(chunkStart, chunkEnd);

            const processed = await processLineOptimized(
              chunkCoordinates,
              resolution,
              segmentId + totalProcessed + lineProcessed,
              lineIndex,
              chunkCounter,
              optimizedBatchSize,
              sessionId,
              abortSignal
            );

            lineProcessed += processed.segmentCount;
            chunkCounter = processed.chunkCounter;

            console.log(`Processed chunk ${Math.floor(chunkStart / chunkSize) + 1} of line ${lineIndex}: ${processed.segmentCount} segments`);
          }

          totalProcessed += lineProcessed;
        } else {
          // Process line normally
          const processed = await processLineOptimized(
            coordinates,
            resolution,
            segmentId + totalProcessed,
            lineIndex,
            chunkCounter,
            optimizedBatchSize,
            sessionId,
            abortSignal
          );

          totalProcessed += processed.segmentCount;
          chunkCounter = processed.chunkCounter;
        }

        // Report progress with memory info
        if (onProgress) {
          onProgress({
            completed: totalProcessed,
            total: totalEstimatedSegments,
            percentage: (totalProcessed / totalEstimatedSegments) * 100,
            currentFeature: featureIndex * coordinateArrays.length + lineIndex + 1,
            totalFeatures: totalFeatures
          });
        }

        // Check memory pressure and adapt
        if (memoryMonitor.isMemoryPressureHigh()) {
          console.log('Memory pressure detected, forcing cleanup');
          memoryMonitor.forceGarbageCollection();
          clearDistanceCache();
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Efficient yielding using requestIdleCallback if available
        if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
          await new Promise(resolve => window.requestIdleCallback(resolve));
        } else {
          await new Promise(resolve => setTimeout(resolve, 5));
        }
      }
    }

    // End performance monitoring
    const cacheStats = getDistanceCacheStats();
    const metrics = performanceMonitor.endSession(
      totalProcessed,
      recommendations.useCompression ? 0.3 : 1.0, // Estimated compression ratio
      cacheStats.hitRate
    );

    console.log(`Optimized segmentation completed: ${totalProcessed} segments in ${metrics.segmentationTime.toFixed(2)}s`);
    console.log('Cache performance:', cacheStats);
    console.log('Processing rate:', `${metrics.processingRate.toFixed(0)} segments/second`);

    // Update chunk metadata with correct total chunks count
    const { indexedDBService } = await import('../services/indexedDBService');
    await updateChunkTotalCounts(sessionId, chunkCounter);

    // Store metadata for the completed segmentation

    try {
      // Store only metadata, not all segments (true zero-memory approach)
      const metadata = {
        id: 'current-segments',
        totalSegments: totalProcessed,
        bounds: [[-90, -180], [90, 180]] as [[number, number], [number, number]], // Will be calculated from first few segments
        timestamp: Date.now(),
        isMemoryOptimized: true, // Always true for streaming mode
        averageSegmentLength: resolution, // Use resolution as estimate
        processingTime: metrics.segmentationTime,
        sessionId: sessionId
      };

      await indexedDBService.storeSegmentMetadata(metadata);
      console.log(`Stored metadata for ${totalProcessed} segments (zero-memory mode)`);

      // Return only a summary object with metadata - NO actual segments
      const summarySegment: ShorelineSegment = {
        type: 'Feature' as const,
        id: 'zero-memory-summary',
        geometry: { type: 'LineString', coordinates: [[0, 0], [0, 0]] },
        properties: {
          id: 'zero-memory-summary',
          index: 0,
          lineIndex: 0,
          length: 0,
          values: {
            isZeroMemorySummary: true,
            totalActualSegments: totalProcessed,
            processingTime: metrics.segmentationTime,
            processingRate: metrics.processingRate,
            cacheHitRate: cacheStats.hitRate,
            displayCount: totalProcessed,
            sessionId: sessionId
          }
        },
        parameters: {}
      };

      console.log(`Zero-memory segmentation completed: ${totalProcessed} segments processed and stored`);
      return [summarySegment]; // Return only summary, not actual segments

    } catch (error) {
      console.error('Failed to store segmentation metadata:', error);
      throw new Error(`Failed to complete zero-memory segmentation: ${error instanceof Error ? error.message : String(error)}`);
    }

  } catch (error) {
    // Clean up on error
    clearDistanceCache();
    clearAllPools();
    throw error;
  }
}

/**
 * Create placeholder segments for fallback scenarios
 */
function createPlaceholderSegments(
  totalProcessed: number,
  metrics: any,
  cacheStats: any
): ShorelineSegment[] {
  const placeholderSegments: ShorelineSegment[] = [];
  for (let i = 0; i < Math.min(totalProcessed, 1000); i++) {
    placeholderSegments.push({
      type: 'Feature' as const,
      id: `placeholder-${i}`,
      geometry: { type: 'LineString', coordinates: [[0, 0], [0, 0]] },
      properties: {
        id: `placeholder-${i}`,
        index: i,
        lineIndex: 0,
        length: 0,
        values: {
          isPlaceholder: true,
          totalActualSegments: totalProcessed,
          processingTime: metrics.segmentationTime,
          processingRate: metrics.processingRate,
          cacheHitRate: cacheStats.hitRate
        }
      },
      parameters: {}
    });
  }
  return placeholderSegments;
}

/**
 * Optimized segment estimation using geodesic calculations
 */
function calculateOptimizedEstimatedSegments(
  shoreline: FeatureCollection<LineString | MultiLineString>,
  resolution: number
): number {
  let totalLength = 0;

  for (const feature of shoreline.features) {
    const lines = feature.geometry.type === 'MultiLineString'
      ? feature.geometry.coordinates.map(coords => ({ type: 'LineString' as const, coordinates: coords }))
      : [feature.geometry];

    for (const line of lines) {
      // Use optimized geodesic distance calculation for better accuracy
      const coords = line.coordinates;
      for (let i = 1; i < coords.length; i++) {
        const [lon1, lat1] = coords[i - 1];
        const [lon2, lat2] = coords[i];
        totalLength += optimizedGeodesicDistance(lat1, lon1, lat2, lon2);
      }
    }
  }

  return Math.ceil(totalLength / resolution);
}

/**
 * Legacy estimation function for backward compatibility
 */
function calculateEstimatedSegments(
  shoreline: FeatureCollection<LineString | MultiLineString>,
  resolution: number
): number {
  let totalLength = 0;

  for (const feature of shoreline.features) {
    const lines = feature.geometry.type === 'MultiLineString'
      ? feature.geometry.coordinates.map(coords => ({ type: 'LineString' as const, coordinates: coords }))
      : [feature.geometry];

    for (const line of lines) {
      // Quick length estimation using coordinate distance
      const coords = line.coordinates;
      for (let i = 1; i < coords.length; i++) {
        const dx = coords[i][0] - coords[i-1][0];
        const dy = coords[i][1] - coords[i-1][1];
        totalLength += Math.sqrt(dx * dx + dy * dy) * 111320; // Rough degrees to meters
      }
    }
  }

  return Math.ceil(totalLength / resolution);
}

/**
 * Optimized line processing with advanced algorithms and memory management
 */
async function processLineOptimized(
  coordinates: Position[],
  resolution: number,
  baseSegmentId: number,
  lineIndex: number,
  initialChunkCounter: number,
  batchSize: number,
  sessionId: string,
  abortSignal?: AbortSignal
): Promise<{ segmentCount: number; chunkCounter: number }> {
  let chunkCounter = initialChunkCounter;
  let processedInLine = 0;

  // Check if coordinate count is safe for processing
  if (!CoordinateBuffer.isSafeSize(coordinates.length)) {
    throw new Error(`Line has too many coordinates (${coordinates.length}). Maximum supported: ${Math.floor(2147483647 / 2)}`);
  }

  // Use CoordinateBuffer for efficient storage
  const coordBuffer = new CoordinateBuffer(coordinates.length);
  try {
    for (const [lon, lat] of coordinates) {
      coordBuffer.addCoordinate(lon, lat);
    }
  } catch (error) {
    coordBuffer.dispose();
    throw new Error(`Failed to process coordinates: ${error instanceof Error ? error.message : String(error)}`);
  }

  // Calculate distances using optimized batch function
  const coordinateArray: [number, number][] = [];
  for (let i = 0; i < coordBuffer.getLength(); i++) {
    coordinateArray.push(coordBuffer.getCoordinate(i));
  }

  let distances: number[];
  try {
    distances = batchGeodesicDistances(coordinateArray);
  } catch (error) {
    coordBuffer.dispose();
    throw new Error(`Failed to calculate distances: ${error instanceof Error ? error.message : String(error)}`);
  }
  const length = distances[distances.length - 1];
  const numSegments = Math.ceil(length / resolution);
  const actualResolution = length / numSegments;

  // Check if segment count is safe for processing
  if (!SegmentMetadataBuffer.isSafeSize(numSegments)) {
    coordBuffer.dispose();
    throw new Error(`Too many segments would be generated (${numSegments}). Maximum supported: ${2147483647}`);
  }

  // Use SegmentMetadataBuffer for efficient metadata storage
  const metadataBuffer = new SegmentMetadataBuffer(Math.min(numSegments, batchSize));
  let segmentBuffer: ShorelineSegment[] = [];

  // Process segments in optimized batches
  for (let i = 0; i < numSegments; i++) {
    if (abortSignal?.aborted) {
      throw new Error('Segmentation cancelled');
    }

    const startDist = i * actualResolution;
    const endDist = Math.min((i + 1) * actualResolution, length);

    const startCoord = interpolateCoordinateOptimized(coordinateArray, distances, startDist);
    const endCoord = interpolateCoordinateOptimized(coordinateArray, distances, endDist);

    // Calculate segment length using optimized geodesic distance
    const segmentLength = optimizedGeodesicDistance(
      startCoord[1], startCoord[0], endCoord[1], endCoord[0]
    );

    // Store metadata efficiently
    metadataBuffer.addSegment(baseSegmentId + i, segmentLength, i);

    // Create segment with original path coordinates for better accuracy
    const segmentCoordinates = extractOriginalPathCoordinates(
      coordinateArray, distances, startDist, endDist
    );

    const segment: ShorelineSegment = {
      type: 'Feature',
      id: `segment-${baseSegmentId + i}`,
      geometry: { type: 'LineString', coordinates: segmentCoordinates },
      properties: {
        id: `segment-${baseSegmentId + i}`,
        index: i,
        lineIndex,
        length: segmentLength,
        values: {}
      },
      parameters: {}
    };

    segmentBuffer.push(segment);
    processedInLine++;

    // Save when buffer reaches optimized batch size
    if (segmentBuffer.length >= batchSize) {
      await saveOptimizedSegmentChunk(segmentBuffer, `${sessionId}-${chunkCounter}`, chunkCounter, -1); // -1 means unknown total
      chunkCounter++;
      segmentBuffer = []; // Clear immediately

      // Efficient yielding
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        await new Promise(resolve => window.requestIdleCallback(resolve));
      } else {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
  }

  // Save remaining segments
  if (segmentBuffer.length > 0) {
    await saveOptimizedSegmentChunk(segmentBuffer, `${sessionId}-${chunkCounter}`, chunkCounter, -1); // -1 means unknown total
    chunkCounter++;
    segmentBuffer = [];
  }

  // Clean up efficiently
  coordBuffer.dispose();
  metadataBuffer.dispose();

  return { segmentCount: processedInLine, chunkCounter };
}

/**
 * Legacy zero-memory processing for backward compatibility
 */
async function processLineZeroMemory(
  coordinates: Position[],
  resolution: number,
  baseSegmentId: number,
  lineIndex: number,
  initialChunkCounter: number,
  sessionId: string,
  abortSignal?: AbortSignal
): Promise<{ segmentCount: number; chunkCounter: number }> {
  // Delegate to optimized version with smaller batch size
  return processLineOptimized(
    coordinates,
    resolution,
    baseSegmentId,
    lineIndex,
    initialChunkCounter,
    100, // Smaller batch size for compatibility
    sessionId,
    abortSignal
  );
}

/**
 * Update all chunks with the correct total chunks count
 */
async function updateChunkTotalCounts(sessionId: string, totalChunks: number): Promise<void> {
  const { indexedDBService } = await import('../services/indexedDBService');

  try {
    console.log(`Updating ${totalChunks} chunks with correct total count...`);
    await indexedDBService.updateChunkTotalCounts(sessionId, totalChunks);
    console.log(`Successfully updated chunk metadata for session: ${sessionId}`);
  } catch (error) {
    console.warn('Failed to update chunk total counts:', error);
  }
}

/**
 * Optimized segment chunk saving with compression
 */
async function saveOptimizedSegmentChunk(
  segments: ShorelineSegment[],
  chunkId: string,
  chunkIndex: number,
  totalChunks: number
): Promise<void> {
  const { indexedDBService } = await import('../services/indexedDBService');

  console.log(`Saving segment chunk: ${chunkId} with ${segments.length} segments (${chunkIndex + 1}/${totalChunks})`);

  try {
    // Use the new optimized storage method with compression
    await indexedDBService.storeSegmentChunk(
      chunkId,
      segments,
      chunkIndex,
      totalChunks
    );

    console.log(`Successfully saved chunk: ${chunkId} (${chunkIndex + 1}/${totalChunks})`);

    // Clear input data immediately
    segments.length = 0;
  } catch (error) {
    console.error(`Failed to save optimized segment chunk ${chunkId}:`, error);
    // Fallback to legacy method
    console.log(`Falling back to legacy storage for chunk: ${chunkId}`);
    await saveLegacySegmentChunk(segments, chunkId);
  }
}

/**
 * Legacy segment chunk saving for backward compatibility
 */
async function saveLegacySegmentChunk(segments: ShorelineSegment[], chunkId: string): Promise<void> {
  const { indexedDBService } = await import('../services/indexedDBService');

  // Create minimal chunk data
  const chunkData = {
    type: 'FeatureCollection' as const,
    features: segments.map(segment => ({
      type: 'Feature' as const,
      geometry: segment.geometry,
      properties: segment.properties
    }))
  };

  // Save to IndexedDB using legacy method
  await indexedDBService.storeShorelineData(`legacy-${chunkId}`, chunkData);

  // Clear input data
  segments.length = 0;
  for (const feature of chunkData.features) {
    (feature as any).geometry = null;
    (feature as any).properties = null;
  }
  chunkData.features.length = 0;
}

/**
 * Backward compatibility alias
 */
async function saveSegmentChunk(segments: ShorelineSegment[], chunkId: string): Promise<void> {
  // Extract chunk index from chunkId (format: sessionId-chunkIndex)
  const parts = chunkId.split('-');
  const chunkIndex = parseInt(parts[parts.length - 1]) || 0;
  return saveOptimizedSegmentChunk(segments, chunkId, chunkIndex, -1); // -1 means unknown total
}

/**
 * Extract original path coordinates for a segment to preserve shoreline accuracy
 * This ensures segments follow the exact original shoreline path
 */
function extractOriginalPathCoordinates(
  coordinates: [number, number][],
  distances: number[],
  startDistance: number,
  endDistance: number
): [number, number][] {
  const result: [number, number][] = [];

  // Find the indices that bracket our segment distances
  let startIndex = -1;
  let endIndex = -1;

  // Find start index - the last coordinate before or at startDistance
  for (let i = 0; i < distances.length; i++) {
    if (distances[i] <= startDistance) {
      startIndex = i;
    } else {
      break;
    }
  }

  // Find end index - the first coordinate at or after endDistance
  for (let i = startIndex >= 0 ? startIndex : 0; i < distances.length; i++) {
    if (distances[i] >= endDistance) {
      endIndex = i;
      break;
    }
  }

  // Fallback if indices not found
  if (startIndex === -1) startIndex = 0;
  if (endIndex === -1) endIndex = coordinates.length - 1;

  // If the segment is very short and falls between two coordinates,
  // use those two coordinates to maintain the original path
  if (startIndex === endIndex) {
    if (startIndex > 0) {
      result.push(coordinates[startIndex - 1]);
    }
    result.push(coordinates[startIndex]);
    if (startIndex < coordinates.length - 1) {
      result.push(coordinates[startIndex + 1]);
    }
  } else {
    // Include all coordinates from startIndex to endIndex to preserve the original path
    for (let i = startIndex; i <= endIndex; i++) {
      result.push(coordinates[i]);
    }
  }

  // Ensure we have at least 2 coordinates for a valid LineString
  if (result.length < 2) {
    if (startIndex > 0) {
      result.unshift(coordinates[startIndex - 1]);
    } else if (endIndex < coordinates.length - 1) {
      result.push(coordinates[endIndex + 1]);
    } else {
      // Last resort: duplicate the coordinate
      result.push(result[0]);
    }
  }

  return result;
}

/**
 * Optimized coordinate interpolation using binary search
 */
function interpolateCoordinateOptimized(
  coordinates: [number, number][],
  distances: number[],
  targetDistance: number
): [number, number] {
  if (targetDistance <= 0) return coordinates[0];
  if (targetDistance >= distances[distances.length - 1]) return coordinates[coordinates.length - 1];

  // Binary search for efficiency with large coordinate arrays
  let left = 0;
  let right = distances.length - 1;

  while (left < right) {
    const mid = Math.floor((left + right) / 2);
    if (distances[mid] < targetDistance) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }

  const i = left;
  if (i === 0) return coordinates[0];

  const segmentStart = distances[i - 1];
  const segmentEnd = distances[i];
  const segmentLength = segmentEnd - segmentStart;

  if (segmentLength === 0) return coordinates[i - 1];

  const ratio = (targetDistance - segmentStart) / segmentLength;

  // Linear interpolation
  const startCoord = coordinates[i - 1];
  const endCoord = coordinates[i];

  const interpolated: [number, number] = [
    startCoord[0] + (endCoord[0] - startCoord[0]) * ratio,
    startCoord[1] + (endCoord[1] - startCoord[1]) * ratio
  ];

  // Snap to nearest original coordinate if very close (within 0.1% of segment)
  if (ratio < 0.001) {
    return startCoord;
  } else if (ratio > 0.999) {
    return endCoord;
  }

  return interpolated;
}

/**
 * Legacy coordinate interpolation for backward compatibility
 */
function interpolateCoordinate(
  coordinates: Position[],
  distances: number[],
  targetDistance: number
): Position {
  if (targetDistance <= 0) return coordinates[0];
  if (targetDistance >= distances[distances.length - 1]) return coordinates[coordinates.length - 1];

  // Find the segment containing the target distance
  for (let i = 1; i < distances.length; i++) {
    if (distances[i] >= targetDistance) {
      const segmentStart = distances[i - 1];
      const segmentEnd = distances[i];
      const segmentLength = segmentEnd - segmentStart;

      if (segmentLength === 0) return coordinates[i - 1];

      const ratio = (targetDistance - segmentStart) / segmentLength;

      // Linear interpolation
      const startCoord = coordinates[i - 1];
      const endCoord = coordinates[i];

      return [
        startCoord[0] + (endCoord[0] - startCoord[0]) * ratio,
        startCoord[1] + (endCoord[1] - startCoord[1]) * ratio
      ];
    }
  }

  return coordinates[coordinates.length - 1];
}
