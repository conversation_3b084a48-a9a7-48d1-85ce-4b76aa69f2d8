/**
 * Optimized parameter assignment operations for CVIc
 * Applies advanced optimization techniques to polygon intersection, value assignment, and CVI calculation
 */

import * as turf from '@turf/turf';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import type { ShorelineSegment, Parameter, ParameterValue, Formula } from '../types';
import { performanceMonitor } from '../services/performanceMonitor';
import { CoordinateBuffer, SegmentMetadataBuffer, MemoryMonitor } from './memoryPool';
import { compressData, decompressData } from './compression';

// Spatial indexing for fast intersection detection
interface SpatialIndex {
  segments: Map<string, { bounds: [number, number, number, number]; segment: ShorelineSegment }>;
  grid: Map<string, string[]>; // Grid cell -> segment IDs
  cellSize: number;
}

/**
 * Build spatial index for segments to accelerate intersection queries
 */
export function buildSpatialIndex(segments: ShorelineSegment[]): SpatialIndex {
  console.log(`Building spatial index for ${segments.length} segments...`);
  const startTime = performance.now();

  // Calculate optimal grid cell size based on segment distribution
  const bounds = calculateBounds(segments);
  const cellSize = calculateOptimalCellSize(bounds, segments.length);

  const spatialIndex: SpatialIndex = {
    segments: new Map(),
    grid: new Map(),
    cellSize
  };

  // Index each segment
  for (const segment of segments) {
    const segmentBounds = calculateSegmentBounds(segment);
    spatialIndex.segments.set(segment.id, { bounds: segmentBounds, segment });

    // Add to grid cells
    const cells = getIntersectingCells(segmentBounds, cellSize, bounds);
    for (const cellKey of cells) {
      if (!spatialIndex.grid.has(cellKey)) {
        spatialIndex.grid.set(cellKey, []);
      }
      spatialIndex.grid.get(cellKey)!.push(segment.id);
    }
  }

  const endTime = performance.now();
  console.log(`Spatial index built in ${(endTime - startTime).toFixed(2)}ms with ${spatialIndex.grid.size} grid cells`);

  return spatialIndex;
}

/**
 * Optimized polygon intersection detection using spatial indexing
 */
export function findIntersectingSegments(
  polygon: GeoJSONPolygon,
  spatialIndex: SpatialIndex
): string[] {
  console.log('Finding intersecting segments with optimized spatial query...');
  const startTime = performance.now();

  const polygonTurf = turf.polygon(polygon.coordinates);
  const polygonBounds = turf.bbox(polygonTurf);
  
  // Get candidate segments from spatial index
  const candidateIds = getCandidateSegments(polygonBounds, spatialIndex);
  console.log(`Spatial index reduced candidates from ${spatialIndex.segments.size} to ${candidateIds.length}`);

  const intersectingIds: string[] = [];
  let intersectionTests = 0;

  // Test only candidate segments for intersection
  for (const segmentId of candidateIds) {
    const segmentData = spatialIndex.segments.get(segmentId);
    if (!segmentData) continue;

    try {
      intersectionTests++;
      if (turf.booleanIntersects(segmentData.segment.geometry, polygonTurf)) {
        intersectingIds.push(segmentId);
      }
    } catch (error) {
      console.warn(`Intersection test failed for segment ${segmentId}:`, error);
    }
  }

  const endTime = performance.now();
  console.log(`Found ${intersectingIds.length} intersecting segments in ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`Performed ${intersectionTests} intersection tests (${((intersectionTests / spatialIndex.segments.size) * 100).toFixed(1)}% of total)`);

  return intersectingIds;
}

/**
 * Optimized batch value assignment with memory management
 */
export async function optimizedApplyParameterValues(
  segments: ShorelineSegment[],
  selectedSegmentIds: string[],
  parameter: Parameter,
  value: string,
  vulnerability: number
): Promise<ShorelineSegment[]> {
  console.log(`Optimized batch assignment: ${selectedSegmentIds.length} segments for parameter "${parameter.name}"`);
  
  performanceMonitor.startSession();
  const memoryMonitor = MemoryMonitor.getInstance();

  // Create lookup set for O(1) selection checking
  const selectedSet = new Set(selectedSegmentIds);
  
  // Prepare parameter value
  const paramValue: ParameterValue = parameter.type === 'numerical'
    ? { type: 'numerical', value: parseFloat(value), vulnerability }
    : { type: 'categorical', value, vulnerability };

  // Use memory-efficient processing
  const batchSize = 1000;
  const updatedSegments: ShorelineSegment[] = [];
  let updateCount = 0;

  for (let i = 0; i < segments.length; i += batchSize) {
    const batch = segments.slice(i, i + batchSize);
    
    for (const segment of batch) {
      if (selectedSet.has(segment.id)) {
        // Check if update is needed
        const currentValue = segment.parameters?.[parameter.id];
        const needsUpdate = !currentValue || 
          currentValue.value !== paramValue.value || 
          currentValue.vulnerability !== paramValue.vulnerability;

        if (needsUpdate) {
          const updatedSegment = {
            ...segment,
            parameters: { ...segment.parameters, [parameter.id]: paramValue },
            properties: {
              ...segment.properties,
              parameters: { ...segment.properties.parameters, [parameter.id]: paramValue }
            }
          };
          updatedSegments.push(updatedSegment);
          updateCount++;
        } else {
          updatedSegments.push(segment);
        }
      } else {
        updatedSegments.push(segment);
      }
    }

    // Memory pressure check
    if (memoryMonitor.isMemoryPressureHigh()) {
      console.log('Memory pressure detected during value assignment, forcing cleanup');
      memoryMonitor.forceGarbageCollection();
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  const metrics = performanceMonitor.endSession(updateCount);
  console.log(`Updated ${updateCount} segments in ${metrics.segmentationTime.toFixed(2)}s`);

  return updatedSegments;
}

/**
 * Optimized CVI calculation with parallel processing and caching
 */
export async function optimizedCalculateCVI(
  segments: ShorelineSegment[],
  parameters: Parameter[],
  formula: Formula
): Promise<{ [segmentId: string]: number }> {
  console.log(`Optimized CVI calculation for ${segments.length} segments using ${formula.name}`);
  
  performanceMonitor.startSession();
  const memoryMonitor = MemoryMonitor.getInstance();

  const cviScores: { [segmentId: string]: number } = {};
  const batchSize = 500; // Smaller batches for CVI calculation
  let processedCount = 0;

  // Pre-validate segments and parameters
  const validSegments = segments.filter(segment => {
    return parameters.every(param => 
      segment.parameters?.[param.id] !== undefined
    );
  });

  console.log(`${validSegments.length}/${segments.length} segments have all required parameters`);

  // Process in batches to manage memory
  for (let i = 0; i < validSegments.length; i += batchSize) {
    const batch = validSegments.slice(i, i + batchSize);
    
    // Calculate CVI for batch
    for (const segment of batch) {
      try {
        const cviScore = calculateSegmentCVI(segment, parameters, formula);
        cviScores[segment.id] = cviScore;
        processedCount++;
      } catch (error) {
        console.warn(`CVI calculation failed for segment ${segment.id}:`, error);
      }
    }

    // Progress reporting and memory management
    if (i % (batchSize * 4) === 0) {
      console.log(`CVI calculation progress: ${processedCount}/${validSegments.length} (${((processedCount / validSegments.length) * 100).toFixed(1)}%)`);
      
      if (memoryMonitor.isMemoryPressureHigh()) {
        console.log('Memory pressure during CVI calculation, forcing cleanup');
        memoryMonitor.forceGarbageCollection();
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  const metrics = performanceMonitor.endSession(processedCount);
  console.log(`CVI calculation completed: ${processedCount} scores in ${metrics.segmentationTime.toFixed(2)}s`);
  console.log(`Processing rate: ${metrics.processingRate.toFixed(0)} calculations/second`);

  return cviScores;
}

// Helper functions
function calculateBounds(segments: ShorelineSegment[]): [number, number, number, number] {
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  
  for (const segment of segments) {
    const coords = segment.geometry.coordinates;
    for (const coord of coords) {
      const [x, y] = coord;
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    }
  }
  
  return [minX, minY, maxX, maxY];
}

function calculateOptimalCellSize(bounds: [number, number, number, number], segmentCount: number): number {
  const [minX, minY, maxX, maxY] = bounds;
  const area = (maxX - minX) * (maxY - minY);
  const targetCells = Math.sqrt(segmentCount / 10); // Aim for ~10 segments per cell
  return Math.sqrt(area / targetCells);
}

function calculateSegmentBounds(segment: ShorelineSegment): [number, number, number, number] {
  const coords = segment.geometry.coordinates;
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  
  for (const coord of coords) {
    const [x, y] = coord;
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x);
    maxY = Math.max(maxY, y);
  }
  
  return [minX, minY, maxX, maxY];
}

function getIntersectingCells(
  bounds: [number, number, number, number],
  cellSize: number,
  globalBounds: [number, number, number, number]
): string[] {
  const [minX, minY, maxX, maxY] = bounds;
  const [globalMinX, globalMinY] = globalBounds;
  
  const startCol = Math.floor((minX - globalMinX) / cellSize);
  const endCol = Math.floor((maxX - globalMinX) / cellSize);
  const startRow = Math.floor((minY - globalMinY) / cellSize);
  const endRow = Math.floor((maxY - globalMinY) / cellSize);
  
  const cells: string[] = [];
  for (let col = startCol; col <= endCol; col++) {
    for (let row = startRow; row <= endRow; row++) {
      cells.push(`${col},${row}`);
    }
  }
  
  return cells;
}

function getCandidateSegments(
  polygonBounds: [number, number, number, number],
  spatialIndex: SpatialIndex
): string[] {
  const cells = getIntersectingCells(
    polygonBounds,
    spatialIndex.cellSize,
    calculateBounds(Array.from(spatialIndex.segments.values()).map(s => s.segment))
  );
  
  const candidateIds = new Set<string>();
  for (const cellKey of cells) {
    const segmentIds = spatialIndex.grid.get(cellKey);
    if (segmentIds) {
      for (const id of segmentIds) {
        candidateIds.add(id);
      }
    }
  }
  
  return Array.from(candidateIds);
}

function calculateSegmentCVI(segment: ShorelineSegment, parameters: Parameter[], formula: Formula): number {
  // Extract parameter values and weights
  const values: number[] = [];
  const weights: number[] = [];
  
  for (const param of parameters) {
    const paramValue = segment.parameters?.[param.id];
    if (paramValue) {
      values.push(paramValue.vulnerability);
      weights.push(param.weight || 1);
    }
  }
  
  if (values.length === 0) return 0;
  
  // Apply formula (simplified - extend based on actual formula types)
  switch (formula.type) {
    case 'geometric-mean':
      return Math.pow(values.reduce((a, b) => a * b, 1), 1 / values.length);
    case 'arithmetic-mean':
      return values.reduce((a, b) => a + b, 0) / values.length;
    default:
      return Math.pow(values.reduce((a, b) => a * b, 1), 1 / values.length);
  }
}
