/**
 * Service for managing shoreline bounds across the application
 * Stores and retrieves the original shoreline bounds to ensure consistent map zooming
 */

import type { FeatureCollection } from 'geojson';
import * as turf from '@turf/turf';
import L from 'leaflet';

class ShorelineBoundsService {
  private static instance: ShorelineBoundsService;
  private storedBounds: L.LatLngBoundsExpression | null = null;
  private isCalculating: boolean = false;

  static getInstance(): ShorelineBoundsService {
    if (!ShorelineBoundsService.instance) {
      ShorelineBoundsService.instance = new ShorelineBoundsService();
    }
    return ShorelineBoundsService.instance;
  }

  /**
   * Calculate and store bounds from original shoreline GeoJSON
   * This should only be called ONCE when the shoreline is first loaded
   */
  calculateAndStoreBounds(geoJSON: FeatureCollection): L.LatLngBoundsExpression | null {
    // Prevent multiple calculations
    if (this.isCalculating) {
      console.log('ShorelineBoundsService: Bounds calculation already in progress');
      return this.storedBounds;
    }

    // If we already have bounds, don't recalculate
    if (this.storedBounds) {
      console.log('ShorelineBoundsService: Bounds already exist, returning stored bounds:', this.storedBounds);
      return this.storedBounds;
    }

    this.isCalculating = true;

    try {
      if (!geoJSON || !geoJSON.features || geoJSON.features.length === 0) {
        console.warn('ShorelineBoundsService: No features in GeoJSON');
        return null;
      }

      console.log('ShorelineBoundsService: Calculating bounds for', geoJSON.features.length, 'features');
      const bbox = turf.bbox(geoJSON);

      if (bbox && bbox.length === 4 && bbox.every((b: number) => isFinite(b)) && bbox[0] <= bbox[2] && bbox[1] <= bbox[3]) {
        // turf.bbox returns [west, south, east, north] = [minLng, minLat, maxLng, maxLat]
        // Leaflet expects [[south, west], [north, east]] = [[minLat, minLng], [maxLat, maxLng]]
        const bounds: L.LatLngBoundsExpression = [[bbox[1], bbox[0]], [bbox[3], bbox[2]]];
        this.storedBounds = bounds;
        console.log('ShorelineBoundsService: ✅ BOUNDS CALCULATED AND STORED:', bounds);
        console.log('ShorelineBoundsService: Original bbox [west, south, east, north]:', bbox);
        return bounds;
      } else {
        console.warn('ShorelineBoundsService: Calculated bbox is invalid:', bbox);
        return null;
      }
    } catch (error) {
      console.error('ShorelineBoundsService: Error calculating bounds:', error);
      return null;
    } finally {
      this.isCalculating = false;
    }
  }

  /**
   * Get the stored shoreline bounds
   */
  getStoredBounds(): L.LatLngBoundsExpression | null {
    if (this.storedBounds) {
      console.log('ShorelineBoundsService: ✅ RETURNING STORED BOUNDS:', this.storedBounds);
    } else {
      console.warn('ShorelineBoundsService: ❌ NO BOUNDS AVAILABLE');
    }
    return this.storedBounds;
  }

  /**
   * Clear the stored bounds (only when new shoreline data is uploaded)
   */
  clearBounds(): void {
    this.storedBounds = null;
    this.isCalculating = false;
    console.log('ShorelineBoundsService: 🗑️ BOUNDS CLEARED');
  }

  /**
   * Check if bounds are available
   */
  hasBounds(): boolean {
    const has = this.storedBounds !== null;
    console.log('ShorelineBoundsService: hasBounds =', has);
    return has;
  }
}

// Export singleton instance
export const shorelineBoundsService = ShorelineBoundsService.getInstance();
