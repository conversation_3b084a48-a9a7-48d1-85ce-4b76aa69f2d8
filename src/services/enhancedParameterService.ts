/**
 * Enhanced Parameter Assignment Service
 * Uses Web Workers and advanced optimization techniques for maximum performance
 */

import type { ShorelineSegment, Parameter, Formula } from '../types';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import { indexedDBService } from './indexedDBService';
import { performanceMonitor } from './performanceMonitor';
import { MemoryMonitor } from '../utils/memoryPool';
import { compressData, decompressData } from '../utils/compression';

interface WorkerTask {
  id: string;
  type: 'POLYGON_INTERSECTION' | 'BATCH_VALUE_ASSIGNMENT' | 'CVI_CALCULATION';
  payload: any;
}

interface WorkerResult {
  type: 'RESULT' | 'PROGRESS' | 'ERROR';
  payload: any;
}

interface CacheEntry {
  timestamp: number;
  data: any;
}

class EnhancedParameterService {
  private worker: Worker | null = null;
  private pendingTasks = new Map<string, { resolve: Function; reject: Function; onProgress?: Function }>();
  private intersectionCache = new Map<string, CacheEntry>();
  private segmentCache = new Map<string, CacheEntry>();
  private cviCache = new Map<string, CacheEntry>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;
  private isInitialized = false;

  /**
   * Initialize the service with Web Worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize Web Worker
      this.worker = new Worker(
        new URL('../workers/parameterAssignmentWorker.ts', import.meta.url),
        { type: 'module' }
      );

      this.worker.onmessage = this.handleWorkerMessage.bind(this);
      this.worker.onerror = this.handleWorkerError.bind(this);

      this.isInitialized = true;
      console.log('Enhanced Parameter Service initialized with Web Worker');
    } catch (error) {
      console.error('Failed to initialize Enhanced Parameter Service:', error);
      throw error;
    }
  }

  /**
   * Handle messages from Web Worker
   */
  private handleWorkerMessage(event: MessageEvent<WorkerResult>): void {
    const { type, payload } = event.data;

    if (type === 'RESULT') {
      const task = this.pendingTasks.get(payload.taskId);
      if (task) {
        task.resolve(payload.result);
        this.pendingTasks.delete(payload.taskId);
        console.log(`Task ${payload.taskId} completed in ${payload.processingTime.toFixed(2)}s`);
      }
    } else if (type === 'PROGRESS') {
      const task = this.pendingTasks.get(payload.taskId);
      if (task && task.onProgress) {
        task.onProgress(payload.completed, payload.total, payload.percentage);
      }
    } else if (type === 'ERROR') {
      const task = this.pendingTasks.get(payload.taskId);
      if (task) {
        task.reject(new Error(payload.error));
        this.pendingTasks.delete(payload.taskId);
      }
    }
  }

  /**
   * Handle Web Worker errors
   */
  private handleWorkerError(error: ErrorEvent): void {
    console.error('Web Worker error:', error);
    // Reject all pending tasks
    this.pendingTasks.forEach(task => {
      task.reject(new Error('Web Worker error'));
    });
    this.pendingTasks.clear();
  }

  /**
   * Execute task in Web Worker with progress tracking
   */
  private async executeWorkerTask<T>(
    type: WorkerTask['type'],
    payload: any,
    onProgress?: (completed: number, total: number, percentage: number) => void
  ): Promise<T> {
    if (!this.worker) {
      throw new Error('Service not initialized');
    }

    const taskId = `${type}-${Date.now()}-${Math.random()}`;
    
    return new Promise<T>((resolve, reject) => {
      this.pendingTasks.set(taskId, { resolve, reject, onProgress });
      
      const task: WorkerTask = {
        id: taskId,
        type,
        payload
      };
      
      this.worker!.postMessage(task);
    });
  }

  /**
   * Find segments intersecting with polygon using optimized spatial indexing
   */
  async findIntersectingSegments(
    polygon: GeoJSONPolygon,
    segments: ShorelineSegment[],
    onProgress?: (completed: number, total: number, percentage: number) => void
  ): Promise<string[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Generate cache key
    const cacheKey = this.generatePolygonCacheKey(polygon, segments.length);
    
    // Check cache first
    const cached = this.intersectionCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log('Using cached intersection result');
      return cached.data;
    }

    console.log(`Finding intersecting segments for polygon with ${segments.length} total segments...`);
    performanceMonitor.startSession();

    try {
      const intersectingIds = await this.executeWorkerTask<string[]>(
        'POLYGON_INTERSECTION',
        { polygon, segments },
        onProgress
      );

      // Cache result
      this.intersectionCache.set(cacheKey, {
        timestamp: Date.now(),
        data: intersectingIds
      });
      this.cleanupCache(this.intersectionCache);

      console.log(`Found ${intersectingIds.length} intersecting segments`);
      return intersectingIds;
    } catch (error) {
      console.error('Polygon intersection failed:', error);
      throw error;
    }
  }

  /**
   * Apply parameter values to selected segments with optimization
   */
  async applyParameterValues(
    segments: ShorelineSegment[],
    selectedSegmentIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number,
    onProgress?: (completed: number, total: number, percentage: number) => void
  ): Promise<ShorelineSegment[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log(`Applying parameter values with enhanced optimization: ${selectedSegmentIds.length} segments`);
    performanceMonitor.startSession();

    try {
      const updatedSegments = await this.executeWorkerTask<ShorelineSegment[]>(
        'BATCH_VALUE_ASSIGNMENT',
        {
          segments,
          selectedSegmentIds,
          parameter,
          value,
          vulnerability
        },
        onProgress
      );

      // Store updated segments with compression
      await this.storeSegmentsOptimized(updatedSegments);

      console.log('Parameter values applied successfully with enhanced optimization');
      return updatedSegments;
    } catch (error) {
      console.error('Enhanced parameter application failed:', error);
      throw error;
    }
  }

  /**
   * Calculate CVI scores using optimized parallel processing
   */
  async calculateCVI(
    segments: ShorelineSegment[],
    parameters: Parameter[],
    formula: Formula,
    onProgress?: (completed: number, total: number, percentage: number) => void
  ): Promise<{ [segmentId: string]: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Generate cache key
    const cacheKey = this.generateCVICacheKey(segments, parameters, formula);
    
    // Check cache first
    const cached = this.cviCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log('Using cached CVI result');
      return cached.data;
    }

    console.log(`Calculating CVI with enhanced optimization using ${formula.name}`);
    performanceMonitor.startSession();

    try {
      const cviScores = await this.executeWorkerTask<{ [segmentId: string]: number }>(
        'CVI_CALCULATION',
        {
          segments,
          parameters,
          formula
        },
        onProgress
      );

      // Cache result
      this.cviCache.set(cacheKey, {
        timestamp: Date.now(),
        data: cviScores
      });
      this.cleanupCache(this.cviCache);

      // Store CVI scores
      await this.storeCVIScores(cviScores);

      console.log(`CVI calculation completed: ${Object.keys(cviScores).length} scores`);
      return cviScores;
    } catch (error) {
      console.error('Enhanced CVI calculation failed:', error);
      throw error;
    }
  }

  /**
   * Store segments with compression optimization
   */
  private async storeSegmentsOptimized(segments: ShorelineSegment[]): Promise<void> {
    try {
      const featuresToStore = segments.map(seg => ({
        type: 'Feature' as const,
        geometry: seg.geometry,
        properties: seg.properties,
      }));

      const geoJSON = {
        type: 'FeatureCollection' as const,
        features: featuresToStore
      };

      // Use compression if available
      if ('CompressionStream' in window) {
        const compressedData = await compressData(geoJSON);
        await indexedDBService.storeCompressedData('current-segments-compressed', compressedData);
        console.log('Stored compressed segments');
      } else {
        await indexedDBService.storeShorelineData('current-segments', geoJSON);
        console.log('Stored uncompressed segments');
      }
    } catch (error) {
      console.error('Failed to store optimized segments:', error);
      throw error;
    }
  }

  /**
   * Store CVI scores with optimization
   */
  private async storeCVIScores(scores: { [segmentId: string]: number }): Promise<void> {
    try {
      await indexedDBService.storeData('cvi-scores', scores);
      console.log('Stored CVI scores');
    } catch (error) {
      console.error('Failed to store CVI scores:', error);
    }
  }

  /**
   * Generate cache key for polygon intersection
   */
  private generatePolygonCacheKey(polygon: GeoJSONPolygon, segmentCount: number): string {
    const coordsStr = JSON.stringify(polygon.coordinates);
    const hash = this.simpleHash(coordsStr);
    return `polygon-${hash}-${segmentCount}`;
  }

  /**
   * Generate cache key for CVI calculation
   */
  private generateCVICacheKey(segments: ShorelineSegment[], parameters: Parameter[], formula: Formula): string {
    const segmentIds = segments.map(s => s.id).sort().join(',');
    const paramIds = parameters.map(p => p.id).sort().join(',');
    const hash = this.simpleHash(segmentIds + paramIds + formula.id);
    return `cvi-${hash}`;
  }

  /**
   * Simple hash function for cache keys
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Clean up old cache entries
   */
  private cleanupCache(cache: Map<string, CacheEntry>): void {
    if (cache.size <= this.MAX_CACHE_SIZE) return;

    const now = Date.now();
    const entries = Array.from(cache.entries());
    
    // Remove expired entries first
    entries.forEach(([key, entry]) => {
      if (now - entry.timestamp > this.CACHE_TTL) {
        cache.delete(key);
      }
    });

    // If still too large, remove oldest entries
    if (cache.size > this.MAX_CACHE_SIZE) {
      const sortedEntries = entries
        .filter(([key]) => cache.has(key))
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = sortedEntries.slice(0, cache.size - this.MAX_CACHE_SIZE);
      toRemove.forEach(([key]) => cache.delete(key));
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats() {
    return {
      intersectionCacheSize: this.intersectionCache.size,
      segmentCacheSize: this.segmentCache.size,
      cviCacheSize: this.cviCache.size,
      pendingTasks: this.pendingTasks.size,
      memoryUsage: MemoryMonitor.getInstance().getCurrentMemoryUsage(),
      isInitialized: this.isInitialized
    };
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.pendingTasks.clear();
    this.intersectionCache.clear();
    this.segmentCache.clear();
    this.cviCache.clear();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const enhancedParameterService = new EnhancedParameterService();
