/**
 * Performance monitoring service for CVIc segmentation optimization
 * Tracks metrics, memory usage, and provides adaptive optimization
 */

interface PerformanceMetrics {
  segmentationTime: number;
  segmentCount: number;
  memoryPeak: number;
  memoryAverage: number;
  cacheHitRate: number;
  compressionRatio: number;
  processingRate: number; // segments per second
  timestamp: number;
}

interface SystemCapabilities {
  availableMemory: number;
  hardwareConcurrency: number;
  compressionSupported: boolean;
  webWorkerSupported: boolean;
  performanceApiSupported: boolean;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private currentSession: Partial<PerformanceMetrics> = {};
  private memoryReadings: number[] = [];
  private sessionStartTime: number = 0;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Detect system capabilities for adaptive optimization
   */
  getSystemCapabilities(): SystemCapabilities {
    const capabilities: SystemCapabilities = {
      availableMemory: this.getAvailableMemory(),
      hardwareConcurrency: navigator.hardwareConcurrency || 4,
      compressionSupported: 'CompressionStream' in window,
      webWorkerSupported: typeof Worker !== 'undefined',
      performanceApiSupported: 'memory' in performance
    };

    console.log('System capabilities detected:', capabilities);
    return capabilities;
  }

  /**
   * Get available memory estimate
   */
  private getAvailableMemory(): number {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      return memInfo.jsHeapSizeLimit || 2147483648; // Default to 2GB if unknown
    }
    
    // Fallback estimation based on user agent
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      return 1073741824; // 1GB for mobile
    }
    return 4294967296; // 4GB for desktop
  }

  /**
   * Start performance monitoring session
   */
  startSession(): void {
    this.sessionStartTime = performance.now();
    this.currentSession = {
      timestamp: Date.now()
    };
    this.memoryReadings = [];
    
    // Start memory monitoring
    this.startMemoryMonitoring();
  }

  /**
   * Record memory usage during processing
   */
  private startMemoryMonitoring(): void {
    const recordMemory = () => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        this.memoryReadings.push(memInfo.usedJSHeapSize);
      }
    };

    // Record memory every 100ms during processing
    const memoryInterval = setInterval(recordMemory, 100);
    
    // Store interval ID for cleanup
    (this.currentSession as any).memoryInterval = memoryInterval;
  }

  /**
   * End performance monitoring session
   */
  endSession(segmentCount: number, compressionRatio?: number, cacheHitRate?: number): PerformanceMetrics {
    const endTime = performance.now();
    const segmentationTime = (endTime - this.sessionStartTime) / 1000;

    // Stop memory monitoring
    if ((this.currentSession as any).memoryInterval) {
      clearInterval((this.currentSession as any).memoryInterval);
    }

    // Calculate memory statistics
    const memoryPeak = Math.max(...this.memoryReadings, 0);
    const memoryAverage = this.memoryReadings.length > 0 
      ? this.memoryReadings.reduce((sum, val) => sum + val, 0) / this.memoryReadings.length 
      : 0;

    const metrics: PerformanceMetrics = {
      segmentationTime,
      segmentCount,
      memoryPeak,
      memoryAverage,
      cacheHitRate: cacheHitRate || 0,
      compressionRatio: compressionRatio || 1,
      processingRate: segmentCount / segmentationTime,
      timestamp: Date.now()
    };

    this.metrics.push(metrics);
    
    // Keep only last 10 sessions
    if (this.metrics.length > 10) {
      this.metrics = this.metrics.slice(-10);
    }

    console.log('Performance metrics recorded:', metrics);
    return metrics;
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    averageProcessingRate: number;
    averageMemoryUsage: number;
    averageCompressionRatio: number;
    averageCacheHitRate: number;
    sessionCount: number;
    lastSession?: PerformanceMetrics;
  } {
    if (this.metrics.length === 0) {
      return {
        averageProcessingRate: 0,
        averageMemoryUsage: 0,
        averageCompressionRatio: 1,
        averageCacheHitRate: 0,
        sessionCount: 0
      };
    }

    const stats = {
      averageProcessingRate: this.metrics.reduce((sum, m) => sum + m.processingRate, 0) / this.metrics.length,
      averageMemoryUsage: this.metrics.reduce((sum, m) => sum + m.memoryAverage, 0) / this.metrics.length,
      averageCompressionRatio: this.metrics.reduce((sum, m) => sum + m.compressionRatio, 0) / this.metrics.length,
      averageCacheHitRate: this.metrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / this.metrics.length,
      sessionCount: this.metrics.length,
      lastSession: this.metrics[this.metrics.length - 1]
    };

    return stats;
  }

  /**
   * Get recommended optimization settings based on performance history
   */
  getOptimizationRecommendations(): {
    batchSize: number;
    useCompression: boolean;
    useWebWorker: boolean;
    memoryThreshold: number;
    estimatedTime?: number;
  } {
    const capabilities = this.getSystemCapabilities();
    const stats = this.getPerformanceStats();

    // Base recommendations
    let batchSize = 1000;
    let useCompression = capabilities.compressionSupported;
    let useWebWorker = capabilities.webWorkerSupported;
    let memoryThreshold = 0.8; // 80% memory threshold

    // Adjust based on performance history
    if (stats.sessionCount > 0) {
      // Adjust batch size based on memory usage
      if (stats.averageMemoryUsage > capabilities.availableMemory * 0.7) {
        batchSize = 500; // Smaller batches for memory-constrained systems
      } else if (stats.averageMemoryUsage < capabilities.availableMemory * 0.3) {
        batchSize = 2000; // Larger batches for systems with plenty of memory
      }

      // Adjust compression based on performance
      if (stats.averageCompressionRatio > 0.8) {
        useCompression = false; // Compression not effective
      }
    }

    // Adjust for mobile devices
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      batchSize = Math.min(batchSize, 500);
      memoryThreshold = 0.7; // More conservative on mobile
    }

    return {
      batchSize,
      useCompression,
      useWebWorker,
      memoryThreshold,
      estimatedTime: this.estimateProcessingTime(100000) // Estimate for 100k segments
    };
  }

  /**
   * Estimate processing time for a given number of segments
   */
  estimateProcessingTime(segmentCount: number): number {
    const stats = this.getPerformanceStats();
    
    if (stats.sessionCount === 0 || stats.averageProcessingRate === 0) {
      // Fallback estimation: assume 1000 segments per second
      return segmentCount / 1000;
    }

    return segmentCount / stats.averageProcessingRate;
  }

  /**
   * Check if system is under memory pressure
   */
  isMemoryPressureHigh(): boolean {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      const usageRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
      return usageRatio > 0.8;
    }
    return false;
  }

  /**
   * Get current memory usage
   */
  getCurrentMemoryUsage(): { used: number; total: number; percentage: number } | null {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      return {
        used: memInfo.usedJSHeapSize,
        total: memInfo.jsHeapSizeLimit,
        percentage: (memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100
      };
    }
    return null;
  }

  /**
   * Clear performance history
   */
  clearHistory(): void {
    this.metrics = [];
  }

  /**
   * Export performance data for analysis
   */
  exportPerformanceData(): string {
    return JSON.stringify({
      systemCapabilities: this.getSystemCapabilities(),
      performanceStats: this.getPerformanceStats(),
      metrics: this.metrics,
      recommendations: this.getOptimizationRecommendations()
    }, null, 2);
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();
