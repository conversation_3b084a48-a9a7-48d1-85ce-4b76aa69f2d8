/**
 * Optimized Parameter Assignment Service
 * Manages spatial indexing, caching, and batch operations for parameter assignment
 */

import type { ShorelineSegment, Parameter, Formula } from '../types';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import {
  buildSpatialIndex,
  findIntersectingSegments,
  optimizedApplyParameterValues,
  optimizedCalculateCVI,
  type SpatialIndex
} from '../utils/optimizedParameterOperations';
import { indexedDBService } from './indexedDBService';
import { performanceMonitor } from './performanceMonitor';
import { MemoryMonitor } from '../utils/memoryPool';

interface CacheEntry {
  timestamp: number;
  data: any;
}

class OptimizedParameterService {
  private spatialIndex: SpatialIndex | null = null;
  private segmentCache = new Map<string, CacheEntry>();
  private intersectionCache = new Map<string, string[]>();
  private cviCache = new Map<string, { [segmentId: string]: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  /**
   * Initialize the service with segments and build spatial index
   */
  async initialize(segments: ShorelineSegment[]): Promise<void> {
    console.log('Initializing optimized parameter service...');
    const startTime = performance.now();

    // Clear old caches
    this.clearCaches();

    // Build spatial index for fast intersection queries
    this.spatialIndex = buildSpatialIndex(segments);

    // Cache segments for quick access
    this.cacheSegments(segments);

    const endTime = performance.now();
    console.log(`Parameter service initialized in ${(endTime - startTime).toFixed(2)}ms`);
  }

  /**
   * Find segments intersecting with polygon using optimized spatial queries
   */
  async findIntersectingSegments(polygon: GeoJSONPolygon): Promise<string[]> {
    if (!this.spatialIndex) {
      throw new Error('Parameter service not initialized. Call initialize() first.');
    }

    // Check cache first
    const polygonKey = this.getPolygonCacheKey(polygon);
    const cached = this.intersectionCache.get(polygonKey);
    if (cached) {
      console.log(`Cache hit for polygon intersection: ${cached.length} segments`);
      return cached;
    }

    // Perform optimized intersection detection
    const intersectingIds = findIntersectingSegments(polygon, this.spatialIndex);

    // Cache result
    this.intersectionCache.set(polygonKey, intersectingIds);
    this.cleanupCache(this.intersectionCache);

    return intersectingIds;
  }

  /**
   * Apply parameter values to selected segments with optimization
   */
  async applyParameterValues(
    segments: ShorelineSegment[],
    selectedSegmentIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number
  ): Promise<ShorelineSegment[]> {
    console.log(`Applying parameter values with optimization...`);

    // Use optimized batch assignment
    const updatedSegments = await optimizedApplyParameterValues(
      segments,
      selectedSegmentIds,
      parameter,
      value,
      vulnerability
    );

    // Store updated segments with compression
    await this.storeSegmentsOptimized(updatedSegments);

    // Update spatial index if needed
    if (this.spatialIndex) {
      console.log('Updating spatial index with modified segments...');
      this.updateSpatialIndex(updatedSegments, selectedSegmentIds);
    }

    return updatedSegments;
  }

  /**
   * Calculate CVI scores with optimization and caching
   */
  async calculateCVI(
    segments: ShorelineSegment[],
    parameters: Parameter[],
    formula: Formula
  ): Promise<{ [segmentId: string]: number }> {
    console.log(`Calculating CVI with optimization...`);

    // Check cache first
    const cacheKey = this.getCVICacheKey(segments, parameters, formula);
    const cached = this.getCachedCVI(cacheKey);
    if (cached) {
      console.log(`Cache hit for CVI calculation: ${Object.keys(cached).length} scores`);
      return cached;
    }

    // Perform optimized CVI calculation
    const cviScores = await optimizedCalculateCVI(segments, parameters, formula);

    // Cache result
    this.cviCache.set(cacheKey, cviScores);
    this.cleanupCache(this.cviCache);

    // Store CVI scores
    await this.storeCVIScores(cviScores);

    return cviScores;
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats() {
    return {
      spatialIndexSize: this.spatialIndex?.segments.size || 0,
      gridCells: this.spatialIndex?.grid.size || 0,
      segmentCacheSize: this.segmentCache.size,
      intersectionCacheSize: this.intersectionCache.size,
      cviCacheSize: this.cviCache.size,
      memoryUsage: MemoryMonitor.getInstance().getCurrentMemoryUsage()
    };
  }

  /**
   * Clear all caches and reset service
   */
  clearCaches(): void {
    this.segmentCache.clear();
    this.intersectionCache.clear();
    this.cviCache.clear();
    this.spatialIndex = null;
    console.log('Parameter service caches cleared');
  }

  /**
   * Preload segments from storage with optimization
   */
  async preloadSegments(): Promise<ShorelineSegment[]> {
    console.log('Preloading segments with optimization...');

    try {
      // Try to get from optimized storage first
      const segments = await this.loadSegmentsOptimized();
      
      if (segments.length > 0) {
        console.log(`Loaded ${segments.length} segments from optimized storage`);
        return segments;
      }

      // Fallback to regular storage
      const geoJSON = await indexedDBService.getShorelineData('current-segments');
      if (geoJSON?.features) {
        const fallbackSegments = geoJSON.features.map(feature => ({
          ...feature,
          parameters: feature.properties?.parameters || {}
        })) as ShorelineSegment[];
        
        console.log(`Loaded ${fallbackSegments.length} segments from fallback storage`);
        return fallbackSegments;
      }

      return [];
    } catch (error) {
      console.error('Failed to preload segments:', error);
      return [];
    }
  }

  // Private helper methods
  private cacheSegments(segments: ShorelineSegment[]): void {
    const timestamp = Date.now();
    for (const segment of segments) {
      this.segmentCache.set(segment.id, { timestamp, data: segment });
    }
    this.cleanupCache(this.segmentCache);
  }

  private getPolygonCacheKey(polygon: GeoJSONPolygon): string {
    // Create a hash of the polygon coordinates for caching
    const coordsStr = JSON.stringify(polygon.coordinates);
    return `poly-${this.simpleHash(coordsStr)}`;
  }

  private getCVICacheKey(segments: ShorelineSegment[], parameters: Parameter[], formula: Formula): string {
    // Create cache key based on segments, parameters, and formula
    const segmentIds = segments.map(s => s.id).sort().join(',');
    const paramIds = parameters.map(p => p.id).sort().join(',');
    return `cvi-${this.simpleHash(segmentIds + paramIds + formula.type)}`;
  }

  private getCachedCVI(cacheKey: string): { [segmentId: string]: number } | null {
    const cached = this.cviCache.get(cacheKey);
    if (cached) {
      return cached;
    }
    return null;
  }

  private updateSpatialIndex(segments: ShorelineSegment[], updatedIds: string[]): void {
    if (!this.spatialIndex) return;

    // Update only the modified segments in the spatial index
    const updatedSet = new Set(updatedIds);
    for (const segment of segments) {
      if (updatedSet.has(segment.id)) {
        const bounds = this.calculateSegmentBounds(segment);
        this.spatialIndex.segments.set(segment.id, { bounds, segment });
      }
    }
  }

  private calculateSegmentBounds(segment: ShorelineSegment): [number, number, number, number] {
    const coords = segment.geometry.coordinates;
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    for (const coord of coords) {
      const [x, y] = coord;
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    }
    
    return [minX, minY, maxX, maxY];
  }

  private async storeSegmentsOptimized(segments: ShorelineSegment[]): Promise<void> {
    try {
      // Store with compression and chunking for large datasets
      const batchSize = 1000;
      const sessionId = `param-${Date.now()}`;

      for (let i = 0; i < segments.length; i += batchSize) {
        const batch = segments.slice(i, i + batchSize);
        const chunkId = `${sessionId}-${Math.floor(i / batchSize)}`;
        
        await indexedDBService.storeSegmentChunk(
          chunkId,
          batch,
          Math.floor(i / batchSize),
          Math.ceil(segments.length / batchSize)
        );
      }

      console.log(`Stored ${segments.length} segments in optimized format`);
    } catch (error) {
      console.error('Failed to store segments optimized:', error);
      // Fallback to regular storage
      const geoJSON = {
        type: 'FeatureCollection' as const,
        features: segments.map(seg => ({
          type: 'Feature' as const,
          geometry: seg.geometry,
          properties: seg.properties
        }))
      };
      await indexedDBService.storeShorelineData('current-segments', geoJSON);
    }
  }

  private async loadSegmentsOptimized(): Promise<ShorelineSegment[]> {
    try {
      // Try to load from optimized storage
      const allSegments = await indexedDBService.getAllSegmentChunks('param');
      return allSegments as ShorelineSegment[];
    } catch (error) {
      console.warn('Failed to load optimized segments:', error);
      return [];
    }
  }

  private async storeCVIScores(cviScores: { [segmentId: string]: number }): Promise<void> {
    try {
      await indexedDBService.storeShorelineData('cvi-scores', {
        type: 'FeatureCollection',
        features: Object.entries(cviScores).map(([id, score]) => ({
          type: 'Feature',
          geometry: { type: 'Point', coordinates: [0, 0] },
          properties: { segmentId: id, cviScore: score }
        }))
      });
    } catch (error) {
      console.error('Failed to store CVI scores:', error);
    }
  }

  private cleanupCache<T>(cache: Map<string, T>): void {
    if (cache.size > this.MAX_CACHE_SIZE) {
      const entries = Array.from(cache.entries());
      const toDelete = entries.slice(0, entries.length - this.MAX_CACHE_SIZE);
      for (const [key] of toDelete) {
        cache.delete(key);
      }
    }
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// Export singleton instance
export const optimizedParameterService = new OptimizedParameterService();
