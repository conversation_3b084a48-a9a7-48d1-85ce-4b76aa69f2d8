/**
 * Segment Loader Service
 * Provides viewport-based segment loading and memory optimization for large datasets
 */

import type { ShorelineSegment } from '../types';
import { indexedDBService } from './indexedDBService';
import * as turf from '@turf/turf';

interface ViewportBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface ViewportLoadOptions {
  bounds: ViewportBounds;
  maxSegments: number;
  zoomLevel: number;
}

interface SegmentPage {
  segments: ShorelineSegment[];
  totalCount: number;
  pageNumber: number;
  hasMore: boolean;
}

interface SessionInfo {
  totalSegments: number;
  isMemoryOptimized: boolean;
  sessionId: string;
  timestamp: number;
}

class SegmentLoaderService {
  private sessionInfo: SessionInfo | null = null;
  private isInitialized = false;
  private segmentCache: Map<string, ShorelineSegment> = new Map();
  private readonly CACHE_SIZE_LIMIT = 5000; // Limit cached segments

  /**
   * Initialize the service with metadata only (no segment loading)
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('SegmentLoaderService: Already initialized');
      return;
    }

    try {
      console.log('SegmentLoaderService: Initializing with metadata-only approach...');

      // Load only metadata, not actual segments
      const metadata = await indexedDBService.getSegmentMetadata('current-segments');
      if (!metadata) {
        console.log('SegmentLoaderService: No segment metadata found');
        return;
      }

      // Create session info from metadata (no segment loading)
      this.sessionInfo = {
        totalSegments: metadata.totalSegments,
        isMemoryOptimized: metadata.isMemoryOptimized,
        sessionId: `session-${Date.now()}`,
        timestamp: Date.now()
      };

      this.isInitialized = true;
      console.log(`SegmentLoaderService: Initialized with metadata for ${metadata.totalSegments} segments (Memory optimized: ${metadata.isMemoryOptimized})`);

    } catch (error) {
      console.error('SegmentLoaderService: Failed to initialize:', error);
      throw error;
    }
  }



  /**
   * Get segments within viewport bounds using streaming access
   */
  async getSegmentsInViewport(options: ViewportLoadOptions): Promise<ShorelineSegment[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const { bounds, maxSegments, zoomLevel } = options;

    try {
      // Use IndexedDB streaming access for viewport segments
      const segments = await indexedDBService.getSegmentsByBounds(bounds, maxSegments);

      // Cache segments for future use
      this.cacheSegments(segments);

      console.log(`SegmentLoaderService: Loaded ${segments.length} segments for viewport via streaming`);
      return segments;

    } catch (error) {
      console.error('SegmentLoaderService: Error loading viewport segments:', error);
      return [];
    }
  }

  /**
   * Cache segments with LRU eviction
   */
  private cacheSegments(segments: ShorelineSegment[]): void {
    for (const segment of segments) {
      // Remove oldest entries if cache is full
      if (this.segmentCache.size >= this.CACHE_SIZE_LIMIT) {
        const firstKey = this.segmentCache.keys().next().value;
        if (firstKey) {
          this.segmentCache.delete(firstKey);
        }
      }

      this.segmentCache.set(segment.id, segment);
    }
  }



  /**
   * Get paginated segments for table view using streaming access
   */
  async getSegmentPage(pageNumber: number, pageSize: number): Promise<SegmentPage> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startIndex = (pageNumber - 1) * pageSize;
    const totalCount = this.sessionInfo?.totalSegments || 0;

    // Use streaming access to get only the required page
    const pageSegments = await indexedDBService.getSegmentRange(startIndex, pageSize);

    // Cache the loaded segments
    this.cacheSegments(pageSegments);

    return {
      segments: pageSegments,
      totalCount,
      pageNumber,
      hasMore: startIndex + pageSize < totalCount
    };
  }

  /**
   * Get ALL segments at once (no pagination or viewport limits)
   * WARNING: This loads all segments into memory - use only when you need ALL segments
   */
  async getAllSegments(): Promise<ShorelineSegment[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('🔄 SegmentLoaderService: Loading ALL segments...');

      // Try to get from optimized storage first
      console.log('🔄 SegmentLoaderService: Trying optimized storage with baseId "current"...');
      const allSegments = await indexedDBService.getAllSegmentChunks('current');
      if (allSegments.length > 0) {
        console.log(`✅ SegmentLoaderService: Loaded ${allSegments.length} segments from optimized storage`);
        return allSegments as ShorelineSegment[];
      }

      console.log('🔄 SegmentLoaderService: No segments from optimized storage, trying fallback...');

      // Fallback to regular storage
      const data = await indexedDBService.getShorelineData('current-segments');
      if (!data || !data.features) {
        console.log('❌ SegmentLoaderService: No segment data found in fallback storage either');
        return [];
      }

      const segments = data.features.map((feature, index) => ({
        id: feature.properties?.id || `segment-${index + 1}`,
        type: 'Feature' as const,
        geometry: feature.geometry,
        properties: {
          ...feature.properties,
          id: feature.properties?.id || `segment-${index + 1}`,
          index: index + 1,
        },
        parameters: feature.properties?.parameters || {}
      })) as ShorelineSegment[];

      console.log(`✅ SegmentLoaderService: Loaded ${segments.length} segments from fallback storage`);
      return segments;
    } catch (error) {
      console.error('❌ SegmentLoaderService: Error loading all segments:', error);
      return [];
    }
  }

  /**
   * Get total segment count
   */
  getTotalSegmentCount(): number {
    return this.sessionInfo?.totalSegments || 0;
  }

  /**
   * Check if memory optimization is enabled
   */
  isMemoryOptimized(): boolean {
    return this.sessionInfo?.isMemoryOptimized || false;
  }

  /**
   * Get session information
   */
  getSessionInfo(): SessionInfo | null {
    return this.sessionInfo;
  }

  /**
   * Clear service data
   */
  clear(): void {
    this.sessionInfo = null;
    this.isInitialized = false;
    this.segmentCache.clear();
    console.log('SegmentLoaderService: Cleared');
  }
}

// Export singleton instance
export const segmentLoaderService = new SegmentLoaderService();
