import { openDB, IDBPDatabase } from 'idb';
import type { FeatureCollection } from 'geojson';
import type { ProcessedImage } from './imageProcessor';
import * as turf from '@turf/turf';
import { compressData, decompressData, isCompressionSupported } from '../utils/compression';

const DB_NAME = 'shorelineDB';
const DB_VERSION = 4; // Increased version for metadata store
const SHORELINE_STORE = 'shorelineData';
const IMAGE_STORE = 'satelliteImages';
const SEGMENT_CHUNKS_STORE = 'segmentChunks'; // New store for optimized segment storage
const SEGMENT_METADATA_STORE = 'segmentMetadata'; // Store for segment metadata

interface ShorelineStore {
  id: string;
  data: FeatureCollection;
  timestamp: number;
  compressed?: boolean;
  originalSize?: number;
  compressedSize?: number;
  bounds?: [[number, number], [number, number]]; // Store bounds as [[minLat, minLng], [maxLat, maxLng]]
}

interface SegmentMetadataStore {
  id: string;
  totalSegments: number;
  bounds: [[number, number], [number, number]];
  timestamp: number;
  isMemoryOptimized: boolean;
  averageSegmentLength: number;
  processingTime: number;
}

interface ImageStore {
  id: string;
  data: ProcessedImage;
  timestamp: number;
}

interface SegmentChunkStore {
  id: string;
  data: ArrayBuffer; // Compressed segment data
  metadata: {
    segmentCount: number;
    chunkIndex: number;
    totalChunks: number;
    originalSize: number;
    compressedSize: number;
    timestamp: number;
  };
}

class IndexedDBService {
  private db: IDBPDatabase | null = null;

  async initialize(): Promise<void> {
    try {
      this.db = await openDB(DB_NAME, DB_VERSION, {
        upgrade(db, oldVersion) {
          // Create the shoreline store if it doesn't exist
          if (!db.objectStoreNames.contains(SHORELINE_STORE)) {
            db.createObjectStore(SHORELINE_STORE, { keyPath: 'id' });
          }

          // Create the satellite image store if it doesn't exist
          if (!db.objectStoreNames.contains(IMAGE_STORE)) {
            db.createObjectStore(IMAGE_STORE, { keyPath: 'id' });
          }

          // Create the segment chunks store for optimized storage (v3+)
          if (oldVersion < 3 && !db.objectStoreNames.contains(SEGMENT_CHUNKS_STORE)) {
            const segmentStore = db.createObjectStore(SEGMENT_CHUNKS_STORE, { keyPath: 'id' });
            segmentStore.createIndex('timestamp', 'metadata.timestamp');
            segmentStore.createIndex('chunkIndex', 'metadata.chunkIndex');
          }

          // Create the segment metadata store (v4+)
          if (oldVersion < 4 && !db.objectStoreNames.contains(SEGMENT_METADATA_STORE)) {
            const metadataStore = db.createObjectStore(SEGMENT_METADATA_STORE, { keyPath: 'id' });
            metadataStore.createIndex('timestamp', 'timestamp');
          }
        },
      });
    } catch (error) {
      console.error('Failed to initialize IndexedDB:', error);
      throw new Error('Failed to initialize database storage. Please try again.');
    }
  }

  // Shoreline data methods
  async storeShorelineData(id: string, data: FeatureCollection, bounds?: [[number, number], [number, number]]): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const originalSize = JSON.stringify(data).length;

      // Calculate bounds if not provided and this is original shoreline data
      let calculatedBounds = bounds;
      if (!calculatedBounds && (id === 'current-shoreline' || id === 'digitized-shoreline')) {
        calculatedBounds = this.calculateBounds(data);
        console.log('IndexedDB: Calculated bounds for shoreline data:', calculatedBounds);
      }

      let storeData: ShorelineStore;

      // Try compression if supported and data is large
      if (isCompressionSupported() && originalSize > 10000) {
        try {
          const compressedData = await compressData(data);
          storeData = {
            id,
            data: compressedData as any, // Store compressed data
            timestamp: Date.now(),
            compressed: true,
            originalSize,
            compressedSize: compressedData.byteLength,
            bounds: calculatedBounds
          };
          console.log(`Compressed shoreline data: ${originalSize} → ${compressedData.byteLength} bytes (${Math.round((1 - compressedData.byteLength / originalSize) * 100)}% reduction)`);
        } catch (compressionError) {
          console.warn('Compression failed, storing uncompressed:', compressionError);
          storeData = {
            id,
            data,
            timestamp: Date.now(),
            compressed: false,
            originalSize,
            bounds: calculatedBounds
          };
        }
      } else {
        storeData = {
          id,
          data,
          timestamp: Date.now(),
          compressed: false,
          originalSize,
          bounds: calculatedBounds
        };
      }

      await this.db!.put(SHORELINE_STORE, storeData);

      if (calculatedBounds) {
        console.log('IndexedDB: Stored shoreline data with bounds:', calculatedBounds);
      }
    } catch (error) {
      console.error('Failed to store shoreline data:', error);
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        throw new Error('Storage quota exceeded. Please try with a smaller file or clear some space.');
      }
      throw new Error('Failed to store shoreline data. Please try again.');
    }
  }

  async getShorelineData(id: string): Promise<FeatureCollection | null> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const result: ShorelineStore | undefined = await this.db!.get(SHORELINE_STORE, id);
      if (!result) return null;

      // Handle compressed data
      if (result.compressed && result.data instanceof ArrayBuffer) {
        try {
          const decompressedData = await decompressData(result.data);
          return decompressedData as FeatureCollection;
        } catch (decompressionError) {
          console.error('Failed to decompress data:', decompressionError);
          throw new Error('Failed to decompress stored data. The data may be corrupted.');
        }
      }

      return result.data as FeatureCollection;
    } catch (error) {
      console.error('Failed to retrieve shoreline data:', error);
      throw new Error('Failed to retrieve shoreline data. Please try again.');
    }
  }

  async getShorelineBounds(id: string): Promise<[[number, number], [number, number]] | null> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      console.log('IndexedDB: Attempting to retrieve bounds for', id);
      const result: ShorelineStore | undefined = await this.db!.get(SHORELINE_STORE, id);

      if (!result) {
        console.warn('IndexedDB: No data found for', id);
        return null;
      }

      if (!result.bounds) {
        console.warn('IndexedDB: No bounds stored for', id);
        // Try to calculate bounds from the stored data as fallback
        if (result.data) {
          console.log('IndexedDB: Attempting to calculate bounds from stored data as fallback');
          let geoJSONData = result.data;

          // Handle compressed data
          if (result.compressed && result.data instanceof ArrayBuffer) {
            try {
              geoJSONData = await decompressData(result.data) as FeatureCollection;
            } catch (decompressionError) {
              console.error('IndexedDB: Failed to decompress data for bounds calculation:', decompressionError);
              return null;
            }
          }

          const calculatedBounds = this.calculateBounds(geoJSONData as FeatureCollection);
          if (calculatedBounds) {
            console.log('IndexedDB: Successfully calculated fallback bounds:', calculatedBounds);
            // Store the calculated bounds for future use
            try {
              result.bounds = calculatedBounds;
              await this.db!.put(SHORELINE_STORE, result);
              console.log('IndexedDB: Stored calculated bounds for future use');
            } catch (storeError) {
              console.warn('IndexedDB: Failed to store calculated bounds:', storeError);
            }
            return calculatedBounds;
          }
        }
        return null;
      }

      console.log('IndexedDB: Successfully retrieved bounds for', id, ':', result.bounds);
      return result.bounds;
    } catch (error) {
      console.error('IndexedDB: Failed to retrieve shoreline bounds for', id, ':', error);
      return null;
    }
  }

  private calculateBounds(data: FeatureCollection): [[number, number], [number, number]] | null {
    try {
      if (!data || !data.features || data.features.length === 0) {
        console.warn('IndexedDB: No features in GeoJSON for bounds calculation');
        return null;
      }

      console.log('IndexedDB: Calculating bounds for', data.features.length, 'features');
      const bbox = turf.bbox(data);

      if (bbox && bbox.length === 4 && bbox.every((b: number) => isFinite(b)) && bbox[0] <= bbox[2] && bbox[1] <= bbox[3]) {
        // turf.bbox returns [west, south, east, north] = [minLng, minLat, maxLng, maxLat]
        // We store as [[minLat, minLng], [maxLat, maxLng]] for Leaflet compatibility
        const bounds: [[number, number], [number, number]] = [[bbox[1], bbox[0]], [bbox[3], bbox[2]]];
        console.log('IndexedDB: Successfully calculated bounds:', bounds);
        console.log('IndexedDB: Original bbox [west, south, east, north]:', bbox);
        return bounds;
      } else {
        console.warn('IndexedDB: Calculated bbox is invalid:', bbox);
        return null;
      }
    } catch (error) {
      console.error('IndexedDB: Error calculating bounds:', error);
      return null;
    }
  }

  async deleteShorelineData(id: string): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.delete(SHORELINE_STORE, id);
    } catch (error) {
      console.error('Failed to delete shoreline data:', error);
      throw new Error('Failed to delete shoreline data. Please try again.');
    }
  }

  async clearAllShorelineData(): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.clear(SHORELINE_STORE);
    } catch (error) {
      console.error('Failed to clear shoreline data:', error);
      throw new Error('Failed to clear shoreline data. Please try again.');
    }
  }

  // Satellite image methods
  async storeSatelliteImage(id: string, data: ProcessedImage): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const storeData: ImageStore = {
        id,
        data,
        timestamp: Date.now(),
      };
      await this.db!.put(IMAGE_STORE, storeData);
    } catch (error) {
      console.error('Failed to store satellite image:', error);
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        throw new Error('Storage quota exceeded. Please try with a smaller file or clear some space.');
      }
      throw new Error('Failed to store satellite image. Please try again.');
    }
  }

  async getSatelliteImage(id: string): Promise<ProcessedImage | null> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const result: ImageStore | undefined = await this.db!.get(IMAGE_STORE, id);
      return result ? result.data : null;
    } catch (error) {
      console.error('Failed to retrieve satellite image:', error);
      throw new Error('Failed to retrieve satellite image. Please try again.');
    }
  }

  async getAllSatelliteImages(): Promise<ProcessedImage[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const allImages: ImageStore[] = await this.db!.getAll(IMAGE_STORE);
      return allImages.map(item => item.data);
    } catch (error) {
      console.error('Failed to retrieve satellite images:', error);
      throw new Error('Failed to retrieve satellite images. Please try again.');
    }
  }

  async deleteSatelliteImage(id: string): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.delete(IMAGE_STORE, id);
    } catch (error) {
      console.error('Failed to delete satellite image:', error);
      throw new Error('Failed to delete satellite image. Please try again.');
    }
  }

  async clearAllSatelliteImages(): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.clear(IMAGE_STORE);
    } catch (error) {
      console.error('Failed to clear satellite images:', error);
      throw new Error('Failed to clear satellite images. Please try again.');
    }
  }

  // Optimized segment chunk storage methods
  async storeSegmentChunk(chunkId: string, segments: any[], chunkIndex: number, totalChunks: number): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const originalSize = JSON.stringify(segments).length;
      const compressedData = await compressData(segments);

      // Handle unknown total chunks (-1) by setting to 1 temporarily
      const effectiveTotalChunks = totalChunks === -1 ? 1 : totalChunks;

      const chunkData: SegmentChunkStore = {
        id: chunkId,
        data: compressedData,
        metadata: {
          segmentCount: segments.length,
          chunkIndex,
          totalChunks: effectiveTotalChunks,
          originalSize,
          compressedSize: compressedData.byteLength,
          timestamp: Date.now()
        }
      };

      await this.db!.put(SEGMENT_CHUNKS_STORE, chunkData);

      if (totalChunks === -1) {
        console.log(`Stored segment chunk ${chunkIndex}: ${segments.length} segments, ${originalSize} → ${compressedData.byteLength} bytes`);
      } else {
        console.log(`Stored segment chunk ${chunkIndex}/${totalChunks}: ${segments.length} segments, ${originalSize} → ${compressedData.byteLength} bytes`);
      }
    } catch (error) {
      console.error('Failed to store segment chunk:', error);
      throw new Error('Failed to store segment chunk. Please try again.');
    }
  }

  async getSegmentChunk(chunkId: string): Promise<any[] | null> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const result: SegmentChunkStore | undefined = await this.db!.get(SEGMENT_CHUNKS_STORE, chunkId);
      if (!result) return null;

      const decompressedData = await decompressData(result.data);
      return decompressedData as any[];
    } catch (error) {
      console.error('Failed to retrieve segment chunk:', error);
      throw new Error('Failed to retrieve segment chunk. Please try again.');
    }
  }

  async getAllSegmentChunks(baseId: string): Promise<any[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      console.log(`🔍 getAllSegmentChunks: Looking for chunks with baseId: "${baseId}"`);

      const allChunks = await this.db!.getAll(SEGMENT_CHUNKS_STORE);
      console.log(`🔍 getAllSegmentChunks: Found ${allChunks.length} total chunks in database`);

      if (allChunks.length > 0) {
        console.log(`🔍 getAllSegmentChunks: Sample chunk IDs:`, allChunks.slice(0, 3).map(c => c.id));
      }

      const matchingChunks = allChunks
        .filter(chunk => chunk.id.startsWith(baseId))
        .sort((a, b) => a.metadata.chunkIndex - b.metadata.chunkIndex);

      console.log(`🔍 getAllSegmentChunks: Found ${matchingChunks.length} matching chunks for baseId "${baseId}"`);

      if (matchingChunks.length > 0) {
        console.log(`🔍 getAllSegmentChunks: Matching chunk IDs:`, matchingChunks.map(c => c.id));
        console.log(`🔍 getAllSegmentChunks: Chunk metadata:`, matchingChunks.map(c => ({
          id: c.id,
          segmentCount: c.metadata.segmentCount,
          chunkIndex: c.metadata.chunkIndex,
          totalChunks: c.metadata.totalChunks
        })));
      }

      const allSegments: any[] = [];
      for (const chunk of matchingChunks) {
        try {
          const segments = await decompressData(chunk.data);
          console.log(`🔍 getAllSegmentChunks: Decompressed chunk ${chunk.id}: ${segments.length} segments`);
          allSegments.push(...segments);
        } catch (error) {
          console.error(`🔍 getAllSegmentChunks: Failed to decompress chunk ${chunk.id}:`, error);
        }
      }

      console.log(`🔍 getAllSegmentChunks: Total segments loaded: ${allSegments.length}`);
      return allSegments;
    } catch (error) {
      console.error('Failed to retrieve all segment chunks:', error);
      throw new Error('Failed to retrieve segment chunks. Please try again.');
    }
  }

  async updateChunkTotalCounts(sessionId: string, totalChunks: number): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const allChunks = await this.db!.getAll(SEGMENT_CHUNKS_STORE);
      const matchingChunks = allChunks.filter(chunk => chunk.id.startsWith(sessionId));

      console.log(`Updating ${matchingChunks.length} chunks with total count: ${totalChunks}`);

      for (const chunk of matchingChunks) {
        chunk.metadata.totalChunks = totalChunks;
        await this.db!.put(SEGMENT_CHUNKS_STORE, chunk);
      }

      console.log(`Updated ${matchingChunks.length} chunks with correct total count`);
    } catch (error) {
      console.error('Failed to update chunk total counts:', error);
      throw new Error('Failed to update chunk total counts. Please try again.');
    }
  }

  async clearSegmentChunks(baseId?: string): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      if (baseId) {
        // Clear specific chunks
        const allChunks = await this.db!.getAll(SEGMENT_CHUNKS_STORE);
        const matchingChunks = allChunks.filter(chunk => chunk.id.startsWith(baseId));

        for (const chunk of matchingChunks) {
          await this.db!.delete(SEGMENT_CHUNKS_STORE, chunk.id);
        }
      } else {
        // Clear all chunks
        await this.db!.clear(SEGMENT_CHUNKS_STORE);
      }
    } catch (error) {
      console.error('Failed to clear segment chunks:', error);
      throw new Error('Failed to clear segment chunks. Please try again.');
    }
  }

  // Segment metadata methods
  async storeSegmentMetadata(metadata: SegmentMetadataStore): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.put(SEGMENT_METADATA_STORE, metadata);
      console.log(`Stored segment metadata: ${metadata.totalSegments} segments, memory optimized: ${metadata.isMemoryOptimized}`);
    } catch (error) {
      console.error('Failed to store segment metadata:', error);
      throw new Error('Failed to store segment metadata. Please try again.');
    }
  }

  async getSegmentMetadata(id: string = 'current-segments'): Promise<SegmentMetadataStore | null> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const result: SegmentMetadataStore | undefined = await this.db!.get(SEGMENT_METADATA_STORE, id);
      return result || null;
    } catch (error) {
      console.error('Failed to retrieve segment metadata:', error);
      return null;
    }
  }

  async deleteSegmentMetadata(id: string = 'current-segments'): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.delete(SEGMENT_METADATA_STORE, id);
    } catch (error) {
      console.error('Failed to delete segment metadata:', error);
      throw new Error('Failed to delete segment metadata. Please try again.');
    }
  }

  // Streaming segment access methods
  async getSegmentRange(startIndex: number, count: number, id: string = 'current-segments'): Promise<any[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const data = await this.getShorelineData(id);
      if (!data || !data.features) {
        return [];
      }

      const endIndex = Math.min(startIndex + count, data.features.length);
      return data.features.slice(startIndex, endIndex).map((feature, index) => ({
        id: feature.properties?.id || `segment-${startIndex + index + 1}`,
        type: 'Feature' as const,
        geometry: feature.geometry,
        properties: {
          ...feature.properties,
          id: feature.properties?.id || `segment-${startIndex + index + 1}`,
          index: startIndex + index + 1,
        },
        parameters: feature.properties?.parameters || {}
      }));
    } catch (error) {
      console.error('Failed to get segment range:', error);
      return [];
    }
  }

  async getSegmentsByBounds(bounds: { north: number; south: number; east: number; west: number }, maxSegments: number = Number.MAX_SAFE_INTEGER, id: string = 'current-segments'): Promise<any[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const data = await this.getShorelineData(id);
      if (!data || !data.features) {
        return [];
      }

      // Simple bounds filtering - can be optimized further with spatial indexing
      const filteredFeatures = data.features.filter(feature => {
        try {
          const bbox = turf.bbox(feature);
          const [minLng, minLat, maxLng, maxLat] = bbox;

          // Check if segment intersects with bounds
          return !(maxLng < bounds.west || minLng > bounds.east ||
                   maxLat < bounds.south || minLat > bounds.north);
        } catch (error) {
          console.warn('Error filtering segment by bounds:', error);
          return false;
        }
      });

      // Return ALL filtered features (no artificial limit unless explicitly requested)
      const limitedFeatures = maxSegments === Number.MAX_SAFE_INTEGER ? filteredFeatures : filteredFeatures.slice(0, maxSegments);
      return limitedFeatures.map((feature, index) => ({
        id: feature.properties?.id || `segment-${index + 1}`,
        type: 'Feature' as const,
        geometry: feature.geometry,
        properties: {
          ...feature.properties,
          id: feature.properties?.id || `segment-${index + 1}`,
          index: index + 1,
        },
        parameters: feature.properties?.parameters || {}
      }));
    } catch (error) {
      console.error('Failed to get segments by bounds:', error);
      return [];
    }
  }

  /**
   * Stream polygon intersection without loading all segments into memory
   * Processes segments in batches to find intersections with the given polygon
   */
  async getSegmentsByPolygonIntersection(
    polygon: any, // GeoJSON Polygon
    batchSize: number = 500,
    onProgress?: (completed: number, total: number) => void,
    id: string = 'current-segments'
  ): Promise<string[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const data = await this.getShorelineData(id);
      if (!data || !data.features) {
        return [];
      }

      const totalFeatures = data.features.length;
      const intersectingIds: string[] = [];
      let processed = 0;

      console.log(`Starting streaming polygon intersection for ${totalFeatures} segments...`);

      // Create turf polygon for intersection testing
      const polygonTurf = turf.polygon(polygon.coordinates);

      // Process features in batches to avoid memory issues
      for (let i = 0; i < totalFeatures; i += batchSize) {
        const batch = data.features.slice(i, i + batchSize);

        for (let j = 0; j < batch.length; j++) {
          const feature = batch[j];
          const featureIndex = i + j;
          try {
            if (turf.booleanIntersects(feature.geometry, polygonTurf)) {
              // Use the actual stored ID from properties, or generate consistent fallback ID
              const segmentId = feature.properties?.id || `segment-${featureIndex + 1}`;
              intersectingIds.push(segmentId);
            }
          } catch (error) {
            console.warn(`Error checking intersection for feature at index ${featureIndex}:`, error);
          }
          processed++;
        }

        // Report progress
        if (onProgress) {
          onProgress(processed, totalFeatures);
        }

        // Yield control to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 1));
      }

      console.log(`Streaming intersection complete: ${intersectingIds.length} segments found`);
      return intersectingIds;
    } catch (error) {
      console.error('Failed to get segments by polygon intersection:', error);
      return [];
    }
  }

  /**
   * Get specific segments by their IDs for value assignment operations
   * Loads only the requested segments to minimize memory usage
   */
  async getSegmentsByIds(segmentIds: string[], id: string = 'current-segments'): Promise<any[]> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const data = await this.getShorelineData(id);
      if (!data || !data.features) {
        return [];
      }

      const segmentIdSet = new Set(segmentIds);
      const matchingSegments: any[] = [];

      for (let i = 0; i < data.features.length; i++) {
        const feature = data.features[i];
        // Use the actual stored ID from properties, or generate consistent fallback ID
        const segmentId = feature.properties?.id || `segment-${i + 1}`;

        if (segmentIdSet.has(segmentId)) {
          matchingSegments.push({
            id: segmentId,
            type: 'Feature' as const,
            geometry: feature.geometry,
            properties: {
              ...feature.properties,
              id: segmentId,
              index: i + 1,
            },
            parameters: feature.properties?.parameters || {}
          });
        }
      }

      console.log(`Loaded ${matchingSegments.length} segments by IDs from ${segmentIds.length} requested`);
      return matchingSegments;
    } catch (error) {
      console.error('Failed to get segments by IDs:', error);
      return [];
    }
  }

  // General methods
  async clearAllData(): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      await this.db!.clear(SHORELINE_STORE);
      await this.db!.clear(IMAGE_STORE);
      await this.db!.clear(SEGMENT_CHUNKS_STORE);
      await this.db!.clear(SEGMENT_METADATA_STORE);
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw new Error('Failed to clear all data. Please try again.');
    }
  }

  // Performance monitoring methods
  async getStorageStats(): Promise<{
    shorelineCount: number;
    imageCount: number;
    segmentChunkCount: number;
    estimatedSize: number;
  }> {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const [shorelineData, imageData, segmentChunks] = await Promise.all([
        this.db!.getAll(SHORELINE_STORE),
        this.db!.getAll(IMAGE_STORE),
        this.db!.getAll(SEGMENT_CHUNKS_STORE)
      ]);

      const estimatedSize =
        shorelineData.reduce((sum, item) => sum + (item.compressedSize || item.originalSize || 0), 0) +
        imageData.reduce((sum, item) => sum + (item.data.metadata?.size || 0), 0) +
        segmentChunks.reduce((sum, chunk) => sum + chunk.metadata.compressedSize, 0);

      return {
        shorelineCount: shorelineData.length,
        imageCount: imageData.length,
        segmentChunkCount: segmentChunks.length,
        estimatedSize
      };
    } catch (error) {
      console.error('Failed to get storage stats:', error);
      return {
        shorelineCount: 0,
        imageCount: 0,
        segmentChunkCount: 0,
        estimatedSize: 0
      };
    }
  }
}

// Export a singleton instance
export const indexedDBService = new IndexedDBService();
