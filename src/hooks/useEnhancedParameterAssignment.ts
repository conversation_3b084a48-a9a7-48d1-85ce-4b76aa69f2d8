/**
 * Enhanced Parameter Assignment Hook
 * Provides maximum performance with Web Workers, advanced caching, and optimization
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import type { ShorelineSegment, Parameter, Formula } from '../types';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import { enhancedParameterService } from '../services/enhancedParameterService';
import { performanceMonitor } from '../services/performanceMonitor';

interface UseEnhancedParameterAssignmentProps {
  segments: ShorelineSegment[];
  parameters: Parameter[];
  onSegmentsUpdate?: (segments: ShorelineSegment[]) => void;
  onError?: (error: string) => void;
}

interface UseEnhancedParameterAssignmentReturn {
  // State
  isInitialized: boolean;
  isProcessing: boolean;
  selectedSegments: string[];
  cviScores: { [segmentId: string]: number };
  
  // Operations
  findIntersectingSegments: (polygon: GeoJSONPolygon) => Promise<string[]>;
  applyParameterValues: (
    selectedIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number
  ) => Promise<void>;
  calculateCVI: (formula: Formula) => Promise<void>;
  selectSegments: (segmentIds: string[]) => void;
  clearSelection: () => void;
  
  // Performance
  performanceStats: any;
  progressInfo: {
    operation: string | null;
    completed: number;
    total: number;
    percentage: number;
  };
  
  // Utilities
  getCompletionPercentage: () => number;
  validateAllParametersAssigned: () => boolean;
}

export function useEnhancedParameterAssignment({
  segments,
  parameters,
  onSegmentsUpdate,
  onError
}: UseEnhancedParameterAssignmentProps): UseEnhancedParameterAssignmentReturn {
  
  // State management
  const [isInitialized, setIsInitialized] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedSegments, setSelectedSegments] = useState<string[]>([]);
  const [cviScores, setCviScores] = useState<{ [segmentId: string]: number }>({});
  const [currentSegments, setCurrentSegments] = useState<ShorelineSegment[]>(segments);
  const [progressInfo, setProgressInfo] = useState({
    operation: null as string | null,
    completed: 0,
    total: 0,
    percentage: 0
  });

  // Initialize service
  useEffect(() => {
    const initializeService = async () => {
      try {
        await enhancedParameterService.initialize();
        setIsInitialized(true);
        console.log('Enhanced parameter assignment service initialized');
      } catch (error) {
        console.error('Failed to initialize enhanced service:', error);
        onError?.(`Initialization failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    };

    initializeService();

    // Cleanup on unmount
    return () => {
      enhancedParameterService.dispose();
    };
  }, [onError]);

  // Update current segments when segments prop changes
  useEffect(() => {
    setCurrentSegments(segments);
  }, [segments]);

  // Progress callback for operations
  const createProgressCallback = useCallback((operation: string) => {
    return (completed: number, total: number, percentage: number) => {
      setProgressInfo({
        operation,
        completed,
        total,
        percentage
      });
    };
  }, []);

  // Clear progress info
  const clearProgress = useCallback(() => {
    setProgressInfo({
      operation: null,
      completed: 0,
      total: 0,
      percentage: 0
    });
  }, []);

  // Enhanced polygon intersection detection
  const findIntersectingSegments = useCallback(async (polygon: GeoJSONPolygon): Promise<string[]> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      const progressCallback = createProgressCallback('Finding intersecting segments');
      
      console.log('Finding intersecting segments with enhanced optimization...');
      
      const intersectingIds = await enhancedParameterService.findIntersectingSegments(
        polygon,
        currentSegments,
        progressCallback
      );

      console.log(`Enhanced intersection detection found ${intersectingIds.length} segments`);
      return intersectingIds;
    } catch (error) {
      console.error('Enhanced intersection detection failed:', error);
      onError?.(`Intersection detection failed: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsProcessing(false);
      clearProgress();
    }
  }, [isInitialized, currentSegments, createProgressCallback, clearProgress, onError]);

  // Enhanced parameter value application
  const applyParameterValues = useCallback(async (
    selectedIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number
  ): Promise<void> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      const progressCallback = createProgressCallback('Applying parameter values');
      
      console.log(`Applying parameter values with enhanced optimization: ${selectedIds.length} segments`);
      
      const updatedSegments = await enhancedParameterService.applyParameterValues(
        currentSegments,
        selectedIds,
        parameter,
        value,
        vulnerability,
        progressCallback
      );

      setCurrentSegments(updatedSegments);
      onSegmentsUpdate?.(updatedSegments);
      
      console.log('Parameter values applied successfully with enhanced optimization');
    } catch (error) {
      console.error('Enhanced parameter application failed:', error);
      onError?.(`Parameter application failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsProcessing(false);
      clearProgress();
    }
  }, [isInitialized, currentSegments, createProgressCallback, clearProgress, onSegmentsUpdate, onError]);

  // Enhanced CVI calculation
  const calculateCVI = useCallback(async (formula: Formula): Promise<void> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      const progressCallback = createProgressCallback('Calculating CVI scores');
      
      console.log(`Calculating CVI with enhanced optimization using ${formula.name}`);
      
      const scores = await enhancedParameterService.calculateCVI(
        currentSegments,
        parameters,
        formula,
        progressCallback
      );

      setCviScores(scores);
      console.log(`Enhanced CVI calculation completed: ${Object.keys(scores).length} scores`);
    } catch (error) {
      console.error('Enhanced CVI calculation failed:', error);
      onError?.(`CVI calculation failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsProcessing(false);
      clearProgress();
    }
  }, [isInitialized, currentSegments, parameters, createProgressCallback, clearProgress, onError]);

  // Segment selection management
  const selectSegments = useCallback((segmentIds: string[]) => {
    setSelectedSegments(prev => {
      const newSelection = [...new Set([...prev, ...segmentIds])];
      console.log(`Enhanced selection updated: ${newSelection.length} segments selected`);
      return newSelection;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedSegments([]);
    console.log('Enhanced selection cleared');
  }, []);

  // Utility functions
  const getCompletionPercentage = useCallback((): number => {
    if (currentSegments.length === 0 || parameters.length === 0) return 0;

    let totalAssignments = 0;
    let completedAssignments = 0;

    currentSegments.forEach(segment => {
      parameters.forEach(parameter => {
        totalAssignments++;
        if (segment.parameters?.[parameter.id]) {
          completedAssignments++;
        }
      });
    });

    return totalAssignments > 0 ? (completedAssignments / totalAssignments) * 100 : 0;
  }, [currentSegments, parameters]);

  const validateAllParametersAssigned = useCallback((): boolean => {
    return currentSegments.every(segment =>
      parameters.every(parameter =>
        segment.parameters?.[parameter.id] !== undefined
      )
    );
  }, [currentSegments, parameters]);

  // Performance statistics
  const performanceStats = useMemo(() => {
    return enhancedParameterService.getPerformanceStats();
  }, [isProcessing]); // Update when processing state changes

  return {
    // State
    isInitialized,
    isProcessing,
    selectedSegments,
    cviScores,
    
    // Operations
    findIntersectingSegments,
    applyParameterValues,
    calculateCVI,
    selectSegments,
    clearSelection,
    
    // Performance
    performanceStats,
    progressInfo,
    
    // Utilities
    getCompletionPercentage,
    validateAllParametersAssigned
  };
}
