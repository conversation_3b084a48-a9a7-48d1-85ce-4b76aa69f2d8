/**
 * Optimized Parameter Assignment Hook
 * Provides high-performance parameter assignment operations with caching and spatial indexing
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import type { ShorelineSegment, Parameter, Formula } from '../types';
import type { Polygon as GeoJSONPolygon } from 'geojson';
import { optimizedParameterService } from '../services/optimizedParameterService';
import { performanceMonitor } from '../services/performanceMonitor';

interface UseOptimizedParameterAssignmentProps {
  segments: ShorelineSegment[];
  parameters: Parameter[];
  onSegmentsUpdate?: (segments: ShorelineSegment[]) => void;
  onError?: (error: string) => void;
}

interface UseOptimizedParameterAssignmentReturn {
  // State
  isInitialized: boolean;
  isProcessing: boolean;
  selectedSegments: string[];
  cviScores: { [segmentId: string]: number };
  
  // Operations
  findIntersectingSegments: (polygon: GeoJSONPolygon) => Promise<string[]>;
  applyParameterValues: (
    selectedIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number
  ) => Promise<void>;
  calculateCVI: (formula: Formula) => Promise<void>;
  selectSegments: (segmentIds: string[]) => void;
  clearSelection: () => void;
  
  // Performance
  performanceStats: any;
  
  // Utilities
  getCompletionPercentage: () => number;
  validateAllParametersAssigned: () => boolean;
}

export function useOptimizedParameterAssignment({
  segments,
  parameters,
  onSegmentsUpdate,
  onError
}: UseOptimizedParameterAssignmentProps): UseOptimizedParameterAssignmentReturn {
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedSegments, setSelectedSegments] = useState<string[]>([]);
  const [cviScores, setCviScores] = useState<{ [segmentId: string]: number }>({});
  const [currentSegments, setCurrentSegments] = useState<ShorelineSegment[]>(segments);

  // Initialize the optimized service when segments change
  useEffect(() => {
    if (segments.length === 0) return;

    const initializeService = async () => {
      try {
        setIsProcessing(true);
        console.log('Initializing optimized parameter assignment service...');
        
        await optimizedParameterService.initialize(segments);
        setIsInitialized(true);
        setCurrentSegments(segments);
        
        console.log('Optimized parameter service ready');
      } catch (error) {
        console.error('Failed to initialize parameter service:', error);
        onError?.(`Failed to initialize optimization: ${error instanceof Error ? error.message : String(error)}`);
      } finally {
        setIsProcessing(false);
      }
    };

    initializeService();
  }, [segments, onError]);

  // Optimized polygon intersection
  const findIntersectingSegments = useCallback(async (polygon: GeoJSONPolygon): Promise<string[]> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      const intersectingIds = await optimizedParameterService.findIntersectingSegments(polygon);
      
      console.log(`Found ${intersectingIds.length} intersecting segments with optimization`);
      return intersectingIds;
    } catch (error) {
      console.error('Optimized intersection detection failed:', error);
      onError?.(`Intersection detection failed: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    } finally {
      setIsProcessing(false);
    }
  }, [isInitialized, onError]);

  // Optimized parameter value application
  const applyParameterValues = useCallback(async (
    selectedIds: string[],
    parameter: Parameter,
    value: string,
    vulnerability: number
  ): Promise<void> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      console.log(`Applying parameter values with optimization: ${selectedIds.length} segments`);
      
      const updatedSegments = await optimizedParameterService.applyParameterValues(
        currentSegments,
        selectedIds,
        parameter,
        value,
        vulnerability
      );

      setCurrentSegments(updatedSegments);
      onSegmentsUpdate?.(updatedSegments);
      
      console.log('Parameter values applied successfully with optimization');
    } catch (error) {
      console.error('Optimized parameter application failed:', error);
      onError?.(`Parameter application failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsProcessing(false);
    }
  }, [isInitialized, currentSegments, onSegmentsUpdate, onError]);

  // Optimized CVI calculation
  const calculateCVI = useCallback(async (formula: Formula): Promise<void> => {
    if (!isInitialized) {
      throw new Error('Service not initialized');
    }

    try {
      setIsProcessing(true);
      console.log(`Calculating CVI with optimization using ${formula.name}`);
      
      const scores = await optimizedParameterService.calculateCVI(
        currentSegments,
        parameters,
        formula
      );

      setCviScores(scores);
      console.log(`CVI calculation completed: ${Object.keys(scores).length} scores`);
    } catch (error) {
      console.error('Optimized CVI calculation failed:', error);
      onError?.(`CVI calculation failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsProcessing(false);
    }
  }, [isInitialized, currentSegments, parameters, onError]);

  // Selection management
  const selectSegments = useCallback((segmentIds: string[]) => {
    setSelectedSegments(prev => {
      const newSelection = [...new Set([...prev, ...segmentIds])];
      console.log(`Selected ${newSelection.length} segments (added ${segmentIds.length})`);
      return newSelection;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedSegments([]);
    console.log('Selection cleared');
  }, []);

  // Performance statistics
  const performanceStats = useMemo(() => {
    if (!isInitialized) return null;
    
    return {
      ...optimizedParameterService.getPerformanceStats(),
      performanceHistory: performanceMonitor.getPerformanceStats()
    };
  }, [isInitialized, selectedSegments.length, Object.keys(cviScores).length]);

  // Completion percentage calculation
  const getCompletionPercentage = useCallback((): number => {
    if (currentSegments.length === 0 || parameters.length === 0) return 0;

    let totalAssignments = 0;
    let completedAssignments = 0;

    for (const segment of currentSegments) {
      for (const parameter of parameters) {
        totalAssignments++;
        if (segment.parameters?.[parameter.id] !== undefined) {
          completedAssignments++;
        }
      }
    }

    return totalAssignments > 0 ? (completedAssignments / totalAssignments) * 100 : 0;
  }, [currentSegments, parameters]);

  // Validation
  const validateAllParametersAssigned = useCallback((): boolean => {
    return currentSegments.every(segment =>
      parameters.every(param => segment.parameters?.[param.id] !== undefined)
    );
  }, [currentSegments, parameters]);

  // Preload optimization
  useEffect(() => {
    const preloadData = async () => {
      try {
        const preloadedSegments = await optimizedParameterService.preloadSegments();
        if (preloadedSegments.length > 0 && preloadedSegments.length !== segments.length) {
          console.log(`Preloaded ${preloadedSegments.length} segments from optimized storage`);
          setCurrentSegments(preloadedSegments);
          onSegmentsUpdate?.(preloadedSegments);
        }
      } catch (error) {
        console.warn('Failed to preload segments:', error);
      }
    };

    if (isInitialized) {
      preloadData();
    }
  }, [isInitialized, segments.length, onSegmentsUpdate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isInitialized) {
        optimizedParameterService.clearCaches();
      }
    };
  }, [isInitialized]);

  return {
    // State
    isInitialized,
    isProcessing,
    selectedSegments,
    cviScores,
    
    // Operations
    findIntersectingSegments,
    applyParameterValues,
    calculateCVI,
    selectSegments,
    clearSelection,
    
    // Performance
    performanceStats,
    
    // Utilities
    getCompletionPercentage,
    validateAllParametersAssigned
  };
}
