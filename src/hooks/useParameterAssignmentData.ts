import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { indexedDBService } from '../services/indexedDBService';
import type { Parameter, ShorelineSegment, ShorelineSegmentProperties, Formula, ParameterValue } from '../types';
import type { LineString, MultiLineString } from 'geojson';
import * as turf from '@turf/turf';
import L from 'leaflet';
import { availableFormulas } from '../config/formulas';

export const useParameterAssignmentData = () => {
  const navigate = useNavigate();
  const [segments, setSegments] = useState<ShorelineSegment[]>([]);
  const [parameters, setParameters] = useState<Parameter[]>([]);
  const [mapBounds, setMapBounds] = useState<L.LatLngBoundsExpression | null>(null);
  const [initialCviScores, setInitialCviScores] = useState<Record<string, number>>({});
  const [initialFormula, setInitialFormula] = useState<Formula | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("Loading data for Parameter Assignment...");

      // Check if we have zero-memory segments (metadata only)
      const metadata = await indexedDBService.getSegmentMetadata('current-segments');
      if (!metadata || metadata.totalSegments === 0) {
        setError('No segments found. Please complete the segmentation step first.');
        navigate('/segment-table');
        setLoading(false);
        return;
      }

      console.log(`Zero-memory mode: ${metadata.totalSegments} segments available for parameter assignment`);

      // For parameter assignment, we use streaming operations without loading all segments
      // Only load a minimal set for UI operations to keep memory usage low
      console.log('Using optimized streaming approach for parameter assignment...');

      // Load only a small sample of segments for UI operations (first 100 segments)
      // All other operations (selection, value assignment) will use streaming from IndexedDB
      const sampleSegments = await indexedDBService.getSegmentRange(0, 100);
      if (sampleSegments.length === 0) {
        setError('No segments could be loaded. Please try again.');
        setLoading(false);
        return;
      }

      console.log(`✅ LOADED SAMPLE: ${sampleSegments.length} segments for UI operations (${metadata.totalSegments} total available via streaming)`);

      // Note: Polygon intersections will work with all segments via streaming from IndexedDB
      const parameterData = await indexedDBService.getShorelineData('current-parameters');
      console.log('Raw parameter data from IndexedDB:', parameterData);
      if (!parameterData || !parameterData.features || parameterData.features.length === 0) {
        console.log('No parameter data found in IndexedDB');
        setError('No parameters found. Please complete the parameter selection step first.');
        navigate('/parameter-selection');
        setLoading(false);
        return;
      }

      // Load the selected index to get the formula
      const indexData = await indexedDBService.getShorelineData('current-index');
      console.log('Raw index data from IndexedDB:', indexData);
      let indexFormula: Formula | null = null;

      if (indexData && indexData.features && indexData.features.length > 0) {
        const savedIndex = indexData.features[0].properties;
        console.log('Loaded saved index:', savedIndex);

        // Map index formula to Formula object
        if (savedIndex.formula) {
          const formulaType = savedIndex.formula;
          let formulaName = savedIndex.shortName || savedIndex.name || 'Unknown Index';
          let formulaDescription = `${formulaName} formula`;

          // Create the formula object based on the index
          indexFormula = {
            type: formulaType as Formula['type'],
            name: formulaName,
            description: formulaDescription
          };

          console.log('Set formula from index:', indexFormula);
        }
      } else {
        console.warn('No index data found - formula will need to be selected manually');
      }

      // Use sample segments for UI operations
      const loadedSegments = sampleSegments.map((segment, index) => {
        const parametersFromProperties: Record<string, ParameterValue> =
          segment.properties?.parameters && typeof segment.properties.parameters === 'object'
          ? segment.properties.parameters
          : segment.parameters || {};

        const finalProperties: ShorelineSegmentProperties = {
           ...segment.properties,
           parameters: parametersFromProperties,
           vulnerabilityIndex: segment.properties?.vulnerabilityIndex,
           vulnerabilityFormula: segment.properties?.vulnerabilityFormula
        };

        return {
          id: segment.id,
          type: 'Feature' as const,
          geometry: segment.geometry,
          properties: finalProperties,
          parameters: parametersFromProperties
        };
      });

      if (loadedSegments.length === 0) throw new Error('No valid line segments found after filtering');
      console.log(`Processed ${loadedSegments.length} segments`);
      setSegments(loadedSegments as ShorelineSegment[]);

      // Use stored shoreline bounds for consistent map zooming (covers entire shoreline)
      const storedBounds = await indexedDBService.getShorelineBounds('current-shoreline');
      if (storedBounds) {
        setMapBounds(storedBounds);
        console.log("ParameterAssignmentPage: ✅ Using stored shoreline bounds from IndexedDB:", storedBounds);
      } else {
        console.warn("ParameterAssignmentPage: No stored bounds available, map will auto-fit to segments");
        // Let map auto-fit to segments if no stored bounds available
      }

      console.log('Parameter features from IndexedDB:', parameterData.features);
      const mappedParameters = parameterData.features.map(feature => {
        console.log('Feature properties:', feature.properties);
        return feature.properties as Parameter;
      });
      console.log('Mapped parameters:', mappedParameters);

      const loadedParameters = mappedParameters.filter((p): p is Parameter => {
        console.log(`Parameter ${p?.name}: enabled=${p?.enabled}, p !== null: ${p !== null}`);
        return p !== null && p.enabled === true;
      });
      console.log(`Processed ${loadedParameters.length} enabled parameters out of ${mappedParameters.length} total`);
      console.log('Final loaded parameters:', loadedParameters);
      setParameters(loadedParameters);

      const existingScores = loadedSegments.reduce((acc, seg) => {
        if (seg.properties.vulnerabilityIndex !== undefined && seg.properties.vulnerabilityIndex !== null) {
          acc[seg.id] = seg.properties.vulnerabilityIndex;
        }
        return acc;
      }, {} as Record<string, number>);

      if (Object.keys(existingScores).length > 0) {
          console.log(`Loaded ${Object.keys(existingScores).length} pre-existing CVI scores`);
          setInitialCviScores(existingScores);
      }

      // Set the formula from the saved index (prioritize index formula over segment formula)
      if (indexFormula) {
          console.log(`Setting formula from saved index: ${indexFormula.name}`);
          setInitialFormula(indexFormula);
      } else {
          // Fallback: try to get formula from segments if no index formula found
          const firstSegmentWithFormula = loadedSegments.find(seg => seg.properties.vulnerabilityFormula);
          if (firstSegmentWithFormula?.properties.vulnerabilityFormula) {
              const formula = availableFormulas.find(f => f.type === firstSegmentWithFormula.properties.vulnerabilityFormula);
              if (formula) {
                  console.log(`Setting fallback formula from segment: ${formula.name}`);
                  setInitialFormula(formula);
              } else {
                   console.warn(`Segment ${firstSegmentWithFormula.id} has unknown formula type: ${firstSegmentWithFormula.properties.vulnerabilityFormula}`);
              }
          }
      }

    } catch (err) {
      console.error('Error loading data:', err);
      setError(`Failed to load assignment data: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  }, [navigate]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    segments,
    setSegments,
    parameters,
    mapBounds,
    initialCviScores,
    initialFormula,
    loading,
    error,
    setError
  };
};
