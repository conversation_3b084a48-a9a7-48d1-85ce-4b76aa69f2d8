// ---- File: src/pages/SegmentationPage.tsx ----
import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import * as turf from '@turf/turf';
import L from 'leaflet';
import Map from '../components/maps/Map';
import { segmentShoreline, SegmentationProgress } from '../utils/geometry';
import { indexedDBService } from '../services/indexedDBService';
import type { FeatureCollection, LineString, MultiLineString } from 'geojson';
import type { ShorelineSegment } from '../types';
import { storeSegmentsOptimized } from '../logic/valueAssignmentLogic';
import { ErrorAlert } from '../components/common/ErrorAlert';
import {
    ArrowLeftIcon,
    ArrowRightIcon,
    InformationCircleIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    BeakerIcon,
    MapPinIcon,
    CubeTransparentIcon,
    Cog6ToothIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';

export default function SegmentationPage() {
  const navigate = useNavigate();
  const [originalGeoJSON, setOriginalGeoJSON] = useState<FeatureCollection<LineString | MultiLineString> | null>(null);
  const [resolution, setResolution] = useState<number | ''>('');
  const [segmentsPreview, setSegmentsPreview] = useState<ShorelineSegment[]>([]);
  const [mapInitialBounds, setMapInitialBounds] = useState<L.LatLngBoundsExpression | null>(null);
  const [totalShorelineLength, setTotalShorelineLength] = useState<number>(0);
  const [estimatedSegments, setEstimatedSegments] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [processing, setProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isPreviewDone, setIsPreviewDone] = useState<boolean>(false);

  // State for segmentation progress and timing
  const [progress, setProgress] = useState<SegmentationProgress | null>(null);
  const [processingTime, setProcessingTime] = useState<number | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    const loadShorelineData = async () => {
      setLoading(true);
      setError(null);
      try {
        const shorelineId = 'current-shoreline';
        const data = await indexedDBService.getShorelineData(shorelineId);
        if (!data) {
          setError('No shoreline data found. Please upload a shapefile first.');
          navigate('/shoreline');
          return;
        }

        if (data.features.every(feature => feature.geometry.type === 'LineString' || feature.geometry.type === 'MultiLineString')) {
          const validGeoJSON = data as FeatureCollection<LineString | MultiLineString>;
          setOriginalGeoJSON(validGeoJSON);

          let totalLengthKm = 0;
          validGeoJSON.features.forEach((feature: any) => {
            try { totalLengthKm += turf.length(turf.feature(feature.geometry), { units: 'kilometers' }); }
            catch (err) { console.warn('Error calculating length for a feature:', err); }
          });
          setTotalShorelineLength(totalLengthKm);

           if (validGeoJSON.features.length > 0) {
               try {
                 const bbox = turf.bbox(validGeoJSON);
                 if (bbox && bbox.length === 4 && bbox.every((b: number) => isFinite(b)) && bbox[0] <= bbox[2] && bbox[1] <= bbox[3]) {
                     const bounds: L.LatLngBoundsExpression = [[bbox[1], bbox[0]], [bbox[3], bbox[2]]];
                     setMapInitialBounds(bounds);
                 } else { console.warn("SegmentationPage: Could not calculate valid initial bounds."); }
               } catch(e) { console.error("SegmentationPage: Error calculating initial bounds:", e); }
           }

        } else {
          throw new Error('Invalid geometry type found in shoreline data. Only LineString or MultiLineString are supported.');
        }
      } catch (err) {
        console.error('Error loading shoreline data:', err);
        setError(`Failed to load shoreline data: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    };
    loadShorelineData();
  }, [navigate]);

  useEffect(() => {
    if (typeof resolution === 'number' && resolution > 0 && totalShorelineLength > 0) {
      const estimated = Math.ceil((totalShorelineLength * 1000) / resolution);
      setEstimatedSegments(estimated);
    } else {
      setEstimatedSegments(0);
    }
  }, [resolution, totalShorelineLength]);

  // Cleanup abort controller on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleResolutionChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const numValue = value === '' ? '' : Number(value);
    setResolution(numValue);
    setError(null);
    setIsPreviewDone(false);
    setSegmentsPreview([]);
  }, []);

  const handleCancelSegmentation = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setProcessing(false);
    setProgress(null);
    setError('Segmentation cancelled by user');
  }, []);

  const handlePreviewSegmentation = useCallback(async () => {
    if (!resolution || resolution <= 0) {
      setError('Resolution must be a positive number (meters).');
      return;
    }
    if (!originalGeoJSON) {
      setError('Original shoreline data is not loaded.');
      return;
    }

    setProcessing(true);
    setError(null);
    setSegmentsPreview([]);
    setIsPreviewDone(false);
    setProgress(null);

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    try {
      const startTime = performance.now();
      console.time("Segmentation Calculation");

      // Use zero-memory streaming segmentation for all datasets
      const generatedSegments = await segmentShoreline(originalGeoJSON, resolution, {
        onProgress: setProgress,
        abortSignal: abortControllerRef.current.signal
      });

      const endTime = performance.now();
      const totalTime = (endTime - startTime) / 1000; // Convert to seconds
      setProcessingTime(totalTime);

      console.timeEnd("Segmentation Calculation");

      // Check if this is zero-memory summary result
      const isZeroMemoryResult = generatedSegments.length === 1 &&
        generatedSegments[0]?.properties?.values?.isZeroMemorySummary;

      if (isZeroMemoryResult) {
        // Zero-memory mode: segments are already stored, just show summary
        const summary = generatedSegments[0];
        const actualCount = summary.properties?.values?.totalActualSegments || 0;
        console.log(`Zero-memory segmentation completed: ${actualCount} segments processed and stored`);

        // Set empty preview array but mark as done
        setSegmentsPreview([]);
        setIsPreviewDone(true);
        setProgress(null);

        // Store the actual count for display
        setEstimatedSegments(actualCount);
      } else {
        // Legacy mode: handle normally (shouldn't happen with current implementation)
        setSegmentsPreview(generatedSegments);
        setIsPreviewDone(true);
        setProgress(null);
        console.log(`Generated ${generatedSegments.length} segments in ${totalTime.toFixed(2)}s`);

        // Store segments with processing time for metadata
        await storeSegmentsOptimized(generatedSegments, totalTime);
      }

    } catch (err) {
      if (err instanceof Error && err.message === 'Segmentation cancelled') {
        console.log('Segmentation was cancelled by user');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to generate segment preview.');
        console.error('Segmentation preview error:', err);
      }
    } finally {
      setProcessing(false);
      setProgress(null);
      abortControllerRef.current = null;
    }
  }, [originalGeoJSON, resolution, estimatedSegments]);

  const handleContinue = useCallback(async () => {
    if (!isPreviewDone) {
      setError("Please generate and preview the segmentation first using the 'Segment Shoreline' button.");
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Check if we have segments in zero-memory mode (segments already stored during processing)
      const metadata = await indexedDBService.getSegmentMetadata('current-segments');

      if (metadata && metadata.totalSegments > 0) {
        // Zero-memory mode: segments are already stored, just proceed
        console.log(`Confirmed ${metadata.totalSegments} segments already stored in zero-memory mode.`);
        navigate('/segment-table');
      } else if (segmentsPreview.length > 0) {
        // Legacy mode: store segments now (shouldn't happen with current implementation)
        await indexedDBService.storeShorelineData('current-segments', {
          type: 'FeatureCollection',
          features: segmentsPreview.map(segment => ({
            type: 'Feature',
            geometry: segment.geometry,
            properties: segment.properties
          }))
        });
        console.log("Confirmed segments stored in IndexedDB.");
        navigate('/segment-table');
      } else {
        setError("No segments found. Please try segmenting again.");
      }
    } catch (err) {
      console.error('Error confirming segments:', err);
      setError(`Failed to confirm segments: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setProcessing(false);
    }
  }, [isPreviewDone, segmentsPreview, navigate]);

  const handleBack = useCallback(() => { navigate('/shoreline'); }, [navigate]);
  const geoJSONForMap = useMemo(() => {
    // Always show original shoreline
    return originalGeoJSON;
  }, [originalGeoJSON]);



  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p className="ml-4 text-gray-600">Loading shoreline data...</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center mb-10">
        <h2 className="text-3xl font-extrabold text-primary-900 tracking-tight">
          2. Segment Shoreline
        </h2>
        <p className="mt-3 text-lg text-gray-600">
          Define the resolution to divide the shoreline into analysis segments.
        </p>
      </div>

      {/* Error Display */}
      <ErrorAlert message={error} onClose={() => setError(null)} />

      {/* Grid Container for Controls and Map */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">

        {/* Left Column: Controls Section */}
        <div className="lg:col-span-1 flex flex-col"> {/* Ensure flex-col for spacing */}
           <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
             <Cog6ToothIcon className="h-6 w-6 mr-2 text-primary-700"/> Segmentation Settings
           </h3>
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-md border border-gray-200 space-y-6"> {/* Use space-y for internal spacing */}
            {/* Info Area */}
            <div className="flex items-center text-sm text-gray-600 border-b border-gray-200 pb-4">
              <MapPinIcon className="h-5 w-5 mr-2 text-primary-600 flex-shrink-0" />
              <span>Total Shoreline Length: <span className="font-medium text-gray-800">{totalShorelineLength.toFixed(2)} km</span></span>
            </div>

            {/* Resolution Input */}
            <div>
              <label htmlFor="resolution" className="block text-sm font-medium text-gray-700 mb-2">
                Segment Resolution/Length <span className="text-red-500">*</span>
              </label>
              <div className="mt-1 relative rounded-md shadow-md border border-gray-200 bg-gray-50 p-1">
                <input
                  type="number"
                  name="resolution"
                  id="resolution"
                  placeholder="Enter resolution (e.g., 10)"
                  value={resolution}
                  onChange={handleResolutionChange}
                  className="block w-full pr-16 py-3 px-3 text-base rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500 disabled:bg-gray-100 bg-white"
                  disabled={processing}
                  aria-describedby="resolution-unit"
                  min="1"
                  step="1"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 text-sm" id="resolution-unit">meters</span>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-600">
                Enter the desired length for each shoreline segment.
              </p>
            </div>

            {/* Estimation & Warning */}
            <div className="p-4 bg-gray-50 rounded-md border border-gray-200 space-y-2">
              <div className="flex items-center text-sm">
                <InformationCircleIcon className="h-5 w-5 mr-2 text-blue-500 flex-shrink-0" />
                <p className="text-gray-700">
                  Estimated segments: <span className="font-semibold">{estimatedSegments.toLocaleString()}</span>
                </p>
              </div>

              {/* Processing mode indicator */}
              <div className="flex items-start text-sm text-blue-600">
                <InformationCircleIcon className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span>Zero-memory streaming: Processing 100 segments at a time, immediate IndexedDB storage</span>
              </div>

              {estimatedSegments > 0 && (
                <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
                  <div className="flex items-center text-sm text-gray-700">
                    <InformationCircleIcon className="h-4 w-4 mr-2 text-gray-500" />
                    <span>
                      Estimated time: {
                        estimatedSegments < 10000 ? '< 10 seconds' :
                        estimatedSegments < 50000 ? '10-30 seconds' :
                        estimatedSegments < 100000 ? '30-60 seconds' :
                        estimatedSegments < 200000 ? '1-2 minutes' :
                        '2-5 minutes'
                      } (varies by dataset size and system performance)
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Progress indicator */}
            {processing && progress && (
              <div className="p-4 bg-blue-50 rounded-md border border-blue-200 space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-blue-700 font-medium">Processing segments...</span>
                  <span className="text-blue-600">{progress.percentage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress.percentage}%` }}
                  ></div>
                </div>
                <div className="flex items-center justify-between text-xs text-blue-600">
                  <span>{progress.completed.toLocaleString()} / {progress.total.toLocaleString()} segments</span>
                  <span>Feature {progress.currentFeature} / {progress.totalFeatures}</span>
                </div>
              </div>
            )}

            {/* Action Buttons: Segment and Cancel */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-start gap-4">
               <button
                 type="button"
                 onClick={handlePreviewSegmentation}
                 disabled={!resolution || resolution <= 0 || processing}
                 className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                 title={!resolution || resolution <= 0 ? "Enter a positive resolution first" : "Segment the shoreline into analysis units"}
               >
                 <CubeTransparentIcon className={`h-5 w-5 mr-2 ${processing && !isPreviewDone ? 'animate-spin' : ''}`} />
                 {processing && !isPreviewDone ? 'Segmenting...' : 'Segment Shoreline'}
               </button>

               {/* Cancel button - only show when processing */}
               {processing && (
                 <button
                   type="button"
                   onClick={handleCancelSegmentation}
                   className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                   title="Cancel the current segmentation process"
                 >
                   <XMarkIcon className="h-5 w-5 mr-2" />
                   Cancel
                 </button>
               )}

               {/* Preview Done Feedback */}
               {isPreviewDone && (
                   <div className="flex flex-col gap-1">
                     <div className="flex items-center text-sm text-green-600">
                         <CheckCircleIcon className="h-5 w-5 mr-1.5"/>
                         <span>
                           {/* Show actual count - either from estimatedSegments (zero-memory) or segmentsPreview (legacy) */}
                           {estimatedSegments > 0
                             ? estimatedSegments.toLocaleString()
                             : segmentsPreview.length.toLocaleString()
                           } segments processed
                         </span>
                     </div>
                     {processingTime && (
                       <div className="text-xs text-gray-500">
                         Processed in {processingTime.toFixed(2)}s
                         <span className="text-green-600 ml-1">
                           (Zero-memory streaming)
                         </span>
                       </div>
                     )}
                   </div>
               )}
            </div>
          </div>
        </div> {/* End Left Column */}


        {/* Right Column: Map Preview Section */}
        <div className="lg:col-span-1 flex flex-col">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <CubeTransparentIcon className="h-6 w-6 mr-2 text-primary-700"/>
                 Preview Map
            </h3>
             <p className="mb-3 text-sm text-gray-600">
               {isPreviewDone
                 ? `${estimatedSegments > 0 ? estimatedSegments.toLocaleString() : segmentsPreview.length.toLocaleString()} segments processed (${resolution}m). Showing original shoreline.`
                 : (originalGeoJSON ? 'Showing original shoreline. Enter resolution & click segment.' : 'Load shoreline data first.')}
            </p>
            <div className="bg-gray-100 rounded-lg shadow-inner border border-gray-200 overflow-hidden flex-grow min-h-[500px] relative"> {/* Added flex-grow and min-h */}
              {originalGeoJSON ? (
                <Map
                  geoJSON={geoJSONForMap}
                  segments={[]} // No segments displayed - show original shoreline only
                  parameters={[]}
                  selectedParameter={null}
                  selectedSegments={[]}
                  selectionPolygons={[]}
                  onSegmentSelect={() => {}}
                  onSelectionDelete={() => {}}
                  onAreaSelect={() => {}}
                  isEditing={false}
                  initialBounds={mapInitialBounds}
                  stylingMode={segmentsPreview.length > 0 ? 'parameter' : undefined} // Show segment styling when segments are available
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                  {loading ? 'Loading map data...' : 'No shoreline data loaded.'}
                </div>
              )}
            </div>
        </div> {/* End Right Column */}

      </div> {/* End Grid Container */}


      {/* Navigation Buttons (Below Grid) */}
      <div className="mt-10 pt-6 border-t border-gray-200 flex justify-between items-center">
        <button
          type="button"
          onClick={handleBack}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2"/> Back
        </button>

        <button
          type="button"
          onClick={handleContinue}
          disabled={!isPreviewDone || processing}
          className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          title={!isPreviewDone ? "Generate segments first" : "Confirm segments and proceed"}
        >
          {processing && isPreviewDone ? 'Confirming...' : 'Confirm & Continue'}
          <ArrowRightIcon className="h-4 w-4 ml-2"/>
        </button>
      </div>
    </div>
  );
}
