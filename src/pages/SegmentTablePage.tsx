// ---- File: src/pages/SegmentTablePage.tsx ----
import { useState, useEffect, useMemo, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import OptimizedMap from '../components/maps/OptimizedMap'
import { indexedDBService } from '../services/indexedDBService'
import { segmentLoaderService } from '../services/segmentLoaderService'
import type { ShorelineSegment } from '../types'
import type { LineString, MultiLineString } from 'geojson'
import * as turf from '@turf/turf'
import L from 'leaflet'
import { ErrorAlert } from '../components/common/ErrorAlert'
import {
    ArrowLeftIcon,
    ArrowRightIcon,
    MagnifyingGlassIcon,
    TableCellsIcon,
    MapIcon as MapOutlineIcon,
    CheckCircleIcon,
} from '@heroicons/react/24/outline';

const ITEMS_PER_PAGE = 10;

export default function SegmentTablePage() {
  const navigate = useNavigate();
  const [segments, setSegments] = useState<ShorelineSegment[]>([]); // Current page segments for table
  const [allSegments, setAllSegments] = useState<ShorelineSegment[]>([]); // ALL segments for map display
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSegmentId, setSelectedSegmentId] = useState<string | null>(null);
  const [sortField, setSortField] = useState<string>('properties.index');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');
  const [mapInitialBounds, setMapInitialBounds] = useState<L.LatLngBoundsExpression | null>(null);
  const [totalSegmentCount, setTotalSegmentCount] = useState(0);
  const [isMemoryOptimized, setIsMemoryOptimized] = useState(false);
  const [originalShoreline, setOriginalShoreline] = useState<FeatureCollection | null>(null);

  useEffect(() => {
    const loadSegments = async () => {
      setLoading(true);
      setError(null);
      try {
        // Initialize segment loader service
        await segmentLoaderService.initialize();

        // Check if we have memory-optimized segments
        const totalCount = segmentLoaderService.getTotalSegmentCount();
        const memoryOptimized = segmentLoaderService.isMemoryOptimized();

        setTotalSegmentCount(totalCount);
        setIsMemoryOptimized(memoryOptimized);

        // No bounds - let map auto-fit to segments

        // Check if we have zero-memory segments (metadata only)
        const metadata = await indexedDBService.getSegmentMetadata('current-segments');
        if (metadata && metadata.totalSegments > 0) {
          // Zero-memory mode: use streaming access
          console.log(`Zero-memory mode: ${metadata.totalSegments} segments available via streaming`);
          setTotalSegmentCount(metadata.totalSegments);
          setIsMemoryOptimized(true);

          // Load only the first page of segments
          const firstPage = await segmentLoaderService.getSegmentPage(1, ITEMS_PER_PAGE);
          setSegments(firstPage.segments);

          console.log(`✅ LOADED FIRST PAGE: ${firstPage.segments.length} segments (${metadata.totalSegments} total)`);

          // Load ALL segments for map display (this is what we want!)
          console.log('Loading ALL segments for map display...');
          const allSegmentsData = await segmentLoaderService.getAllSegments();
          setAllSegments(allSegmentsData);
          console.log(`✅ LOADED ALL SEGMENTS FOR MAP: ${allSegmentsData.length} segments`);
        } else {
          // Fallback: try to load from legacy storage (shouldn't happen with current implementation)
          const data = await indexedDBService.getShorelineData('current-segments');
          if (!data) {
            setError('No segment data found. Please complete the segmentation step first.');
            navigate('/segmentation');
            return;
          }

          const loadedSegments = data.features
            .filter(feature =>
              feature && feature.geometry && (
                feature.geometry.type === 'LineString' ||
                feature.geometry.type === 'MultiLineString'
              )
            )
            .map((feature, index) => {
              const segmentId = feature.properties?.id || `segment-${index + 1}`;
               let length = feature.properties?.length;
               if (length === undefined || length === null) {
                 try {
                   length = turf.length(turf.feature(feature.geometry), { units: 'meters' });
                 } catch (e) {
                   console.warn(`Could not calculate length for segment ${segmentId}:`, e);
                   length = 0;
                 }
               }
              return {
                id: segmentId,
                type: 'Feature' as const,
                geometry: feature.geometry as LineString | MultiLineString,
                properties: {
                  ...feature.properties,
                  id: segmentId,
                  index: index + 1,
                  length: length,
                },
                parameters: {}
              };
            });

          if (loadedSegments.length === 0) {
            throw new Error('No valid line segments found in the data.');
          }

          console.log(`✅ LEGACY MODE: Loaded ${loadedSegments.length} segments`);
          setSegments(loadedSegments);
          setAllSegments(loadedSegments); // In legacy mode, all segments are loaded
          setTotalSegmentCount(loadedSegments.length);
          setIsMemoryOptimized(false);
        }

        // Load original shoreline for map display
        const originalShorelineData = await indexedDBService.getShorelineData('current-shoreline');
        if (originalShorelineData) {
          setOriginalShoreline(originalShorelineData);
          console.log("SegmentTablePage: ✅ Loaded original shoreline for map display");
        } else {
          console.warn("SegmentTablePage: No original shoreline data found");
        }

        // Use stored shoreline bounds for consistent map zooming (covers entire shoreline)
        const storedBounds = await indexedDBService.getShorelineBounds('current-shoreline');
        if (storedBounds) {
          setMapInitialBounds(storedBounds);
          console.log("SegmentTablePage: ✅ Using stored shoreline bounds from IndexedDB:", storedBounds);
        } else {
          console.warn("SegmentTablePage: No stored bounds available, using default bounds");
          // Use default bounds if no stored bounds available
          setMapInitialBounds([[-90, -180], [90, 180]]);
        }
      } catch (err) {
        console.error('Error loading segments:', err);
        setError(`Failed to load segment data: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    };

    loadSegments();
  }, [navigate]);

  const filteredSegments = useMemo(() => {
    return segments.filter(segment => {
      const searchStr = searchTerm.toLowerCase();
      return (
        segment.id.toLowerCase().includes(searchStr) ||
        String(segment.properties.index).includes(searchStr)
      );
    });
  }, [segments, searchTerm]);

  const sortedSegments = useMemo(() => {
    return [...filteredSegments].sort((a, b) => {
      let aValue: any = a;
      let bValue: any = b;
      const fields = sortField.split('.');
      for (const field of fields) {
        aValue = aValue?.[field];
        bValue = bValue?.[field];
      }
      if (aValue === undefined || aValue === null) return sortDirection === 'asc' ? -1 : 1;
      if (bValue === undefined || bValue === null) return sortDirection === 'asc' ? 1 : -1;
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredSegments, sortField, sortDirection]);

  const paginatedSegments = useMemo(() => {
    if (isMemoryOptimized) {
      // For memory-optimized mode, segments are already paginated
      return segments;
    } else {
      // For legacy mode, paginate in memory
      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
      return sortedSegments.slice(startIndex, startIndex + ITEMS_PER_PAGE);
    }
  }, [sortedSegments, currentPage, segments, isMemoryOptimized]);

  const totalPages = Math.ceil((isMemoryOptimized ? totalSegmentCount : filteredSegments.length) / ITEMS_PER_PAGE);
  const handleSegmentSelect = useCallback((segmentId: string) => {
    const newSelectedId = segmentId === selectedSegmentId ? null : segmentId;
    setSelectedSegmentId(newSelectedId);
  }, [selectedSegmentId]);

  const handleSortChange = useCallback((field: string) => {
    if (field === sortField) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  }, [sortField]);

  const handleBack = useCallback(() => navigate('/segmentation'), [navigate]);

  const handleContinue = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("Continuing to parameter selection...");
      navigate('/parameter-selection');
    } catch (err) {
      console.error('Error preparing to continue:', err);
      setError('Failed to proceed. Please try again.');
      setLoading(false);
    }
  }, [navigate]);

  const handlePageChange = useCallback(async (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);

      // For memory-optimized mode, load the new page
      if (isMemoryOptimized) {
        setLoading(true);
        try {
          const pageData = await segmentLoaderService.getSegmentPage(newPage, ITEMS_PER_PAGE);
          setSegments(pageData.segments);
        } catch (err) {
          console.error('Error loading page:', err);
          setError('Failed to load page data');
        } finally {
          setLoading(false);
        }
      }
    }
  }, [totalPages, isMemoryOptimized]);

  const geoJSONForMap = useMemo(() => {
    const features: any[] = [];

    // Always include original shoreline for context
    if (originalShoreline?.features) {
      features.push(...originalShoreline.features.map(feature => ({
        ...feature,
        properties: {
          ...feature.properties,
          isOriginalShoreline: true
        }
      })));
    }

    // Add ALL segments for map display (not just current page)
    if (allSegments && allSegments.length > 0) {
      features.push(...allSegments.map(segment => ({
        type: 'Feature' as const,
        geometry: segment.geometry,
        properties: { ...segment.properties, id: segment.id, isSegment: true }
      })));
    }

    return features.length > 0 ? {
      type: 'FeatureCollection' as const,
      features
    } : null;
  }, [allSegments, originalShoreline]); // Use allSegments instead of segments

  const selectedSegmentIds = useMemo(() => {
    return selectedSegmentId ? [selectedSegmentId] : [];
  }, [selectedSegmentId]);

  if (loading && segments.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p className="ml-4 text-gray-600">Loading segments...</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"> {/* Use larger max-width for side-by-side */}

      {/* Header */}
      <div className="text-center mb-10">
        <h2 className="text-3xl font-extrabold text-primary-900 tracking-tight">
          3. Review Segments
        </h2>
        <p className="mt-3 text-lg text-gray-600">
          Review the generated shoreline segments. Click a row to view on map.
        </p>
        <p className="mt-1 text-sm text-gray-500">
          Total Segments: {totalSegmentCount.toLocaleString()}
          {isMemoryOptimized && <span className="ml-2 text-blue-600">(Memory Optimized)</span>}
        </p>
      </div>

      {/* Error Display */}
      <ErrorAlert message={error} onClose={() => setError(null)} />

      {/* Grid Container */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">

        {/* Left Column: Table and Controls */}
        <div className="lg:col-span-1 flex flex-col space-y-6">
          {/* Table Section */}
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 flex-grow flex flex-col"> {/* Added flex-grow and flex-col */}
            <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0"> {/* Prevent shrinking */}
              <h3 className="text-xl font-semibold text-gray-800 flex items-center">
                <TableCellsIcon className="h-6 w-6 mr-2 text-primary-700"/> Segment List
              </h3>
              <div className="relative w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  placeholder="Search by ID or #"
                  value={searchTerm}
                  onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1); }}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>

            {/* Table Container with scroll */}
            <div className="flex-grow overflow-auto border border-gray-200 rounded-md"> {/* Allow table to grow and scroll */}
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10"> {/* Make header sticky */}
                  <tr>
                    {/* Index Column */}
                    <th
                      scope="col"
                      className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSortChange('properties.index')}
                    >
                      #
                      {sortField === 'properties.index' && (
                        <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    {/* Length Column */}
                    <th
                      scope="col"
                      className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSortChange('properties.length')}
                    >
                      Length (m)
                      {sortField === 'properties.length' && (
                        <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                      )}
                    </th>
                    {/* REMOVED Segment ID Column Header */}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedSegments.map((segment) => (
                    <tr
                      key={segment.id}
                      onClick={() => handleSegmentSelect(segment.id)}
                      className={`hover:bg-primary-50 cursor-pointer transition-colors duration-150 ${
                        selectedSegmentId === segment.id ? 'bg-primary-100 font-medium' : ''
                      }`}
                    >
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center w-16">
                        {segment.properties.index}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center w-32">
                        {segment.properties?.length ? segment.properties.length.toFixed(1) : 'N/A'}
                      </td>
                       {/* REMOVED Segment ID Column Body Cell */}
                    </tr>
                  ))}
                  {paginatedSegments.length === 0 && (
                    <tr>
                      {/* ADJUSTED COLSPAN */}
                      <td colSpan={2} className="px-6 py-4 text-center text-sm text-gray-500">
                        {searchTerm ? 'No segments match search.' : 'No segments loaded.'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Compact Pagination */}
            {totalPages > 1 && (
              <nav
                className="mt-4 flex items-center justify-between border-t border-gray-200 px-1 pt-3 flex-shrink-0" // Prevent shrinking
                aria-label="Pagination"
              >
                 <p className="text-sm text-gray-700 flex-shrink-0 mr-4"> {/* Prevent shrinking */}
                    Showing <span className="font-medium">{(currentPage - 1) * ITEMS_PER_PAGE + 1}</span>
                    - <span className="font-medium">{Math.min(currentPage * ITEMS_PER_PAGE, isMemoryOptimized ? totalSegmentCount : filteredSegments.length)}</span>
                    {' '}of{' '}
                    <span className="font-medium">{isMemoryOptimized ? totalSegmentCount : filteredSegments.length}</span>
                 </p>
                  <div className="flex space-x-1"> {/* Use space-x for button spacing */}
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
              </nav>
            )}
          </div> {/* End Table Section */}

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center flex-shrink-0 mt-auto"> {/* Pushes buttons down */}
             <button
               type="button"
               onClick={handleBack}
               className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
               disabled={loading}
             >
               <ArrowLeftIcon className="h-4 w-4 mr-2"/> Back
             </button>

             <button
               type="button"
               onClick={handleContinue}
               className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
               disabled={loading || totalSegmentCount === 0}
               title={totalSegmentCount === 0 ? "No segments loaded" : "Confirm segments and proceed to parameter selection"}
             >
               {loading ? 'Processing...' : 'Confirm & Continue'}
               <ArrowRightIcon className="h-4 w-4 ml-2"/>
             </button>
           </div>

        </div> {/* End Left Column */}


        {/* Right Column: Map */}
        <div className="lg:col-span-1 flex flex-col">
           <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <MapOutlineIcon className="h-6 w-6 mr-2 text-primary-700"/> Map Preview
            </h3>

            {/* Map Legend */}
            <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <div className="w-4 h-0.5 bg-green-500 mr-2" style={{borderTop: '2px dashed #22c55e'}}></div>
                  <span className="text-gray-700">Original Shoreline</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-0.5 bg-blue-500 mr-2"></div>
                  <span className="text-gray-700">All Segments ({allSegments.length.toLocaleString()})</span>
                </div>
              </div>
            </div>

            {selectedSegmentId && (
               <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-800 flex items-center">
                 <CheckCircleIcon className="h-5 w-5 mr-2 text-blue-600 flex-shrink-0" />
                 Highlighting segment #{segments.find(s => s.id === selectedSegmentId)?.properties.index || ''}. Map zoomed to selection.
               </div>
             )}
          {/* Map container needs defined height */}
          <div className="bg-gray-100 rounded-lg shadow-inner border border-gray-200 overflow-hidden h-[600px] lg:h-full lg:min-h-[600px] flex-grow"> {/* Adjust height as needed, flex-grow */}
             {geoJSONForMap ? (
              <OptimizedMap
                segments={allSegments} // Pass ALL segments to map
                geoJSON={geoJSONForMap}
                parameters={[]}
                selectedParameter={null}
                selectedSegments={selectedSegmentIds}
                selectionPolygons={[]}
                onSegmentSelect={handleSegmentSelect}
                onSelectionDelete={() => {}}
                onAreaSelect={() => {}}
                isEditing={false}
                initialBounds={mapInitialBounds}
                zoomToFeatureId={selectedSegmentId}
                stylingMode="parameter"
                maxSegmentsInViewport={Number.MAX_SAFE_INTEGER} // No limit!
              />
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                {loading ? 'Loading map data...' : 'No shoreline data available'}
              </div>
            )}
          </div>
        </div> {/* End Right Column */}

      </div> {/* End Grid Container */}

    </div>
  );
}
